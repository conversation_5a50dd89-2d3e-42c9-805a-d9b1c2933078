import { v4 as uuidv4 } from 'uuid';

/**
 * Session status enumeration
 */
export type SessionStatus = 'initializing' | 'qr_pending' | 'connecting' | 'connected' | 'disconnected' | 'error';

/**
 * Authentication state interface for Baileys
 */
export interface AuthenticationState {
  creds: any; // AuthenticationCreds from Baileys
  keys: any;  // SignalKeyStoreWithTransaction from <PERSON><PERSON>
}

/**
 * Session domain entity representing a WhatsApp session
 * Contains all business logic for session lifecycle management
 */
export class SessionEntity {
  constructor(
    public readonly userId: string,
    public readonly sessionId: string,
    public phoneNumber: string | null,
    public authState: AuthenticationState | null,
    public status: SessionStatus,
    public readonly createdAt: Date,
    public updatedAt: Date,
    public connectedAt?: Date,
    public disconnectedAt?: Date,
    public ttl?: number, // Unix timestamp for DynamoDB TTL
    public deviceName?: string,
    public browserName?: string
  ) {
    this.validateUserId(userId);
    this.validateSessionId(sessionId);
  }

  /**
   * Factory method to create a new session
   */
  static create(
    userId: string,
    deviceName?: string,
    browserName?: string
  ): SessionEntity {
    const now = new Date();
    const sessionId = uuidv4();
    const ttl = Math.floor((now.getTime() + (72 * 60 * 60 * 1000)) / 1000); // 72 hours from now

    return new SessionEntity(
      userId,
      sessionId,
      null, // phoneNumber initially null
      null, // authState initially null
      'initializing',
      now,
      now,
      undefined, // connectedAt
      undefined, // disconnectedAt
      ttl,
      deviceName || 'WhatsApp Manager',
      browserName || 'Chrome'
    );
  }

  /**
   * Mark session as connected with phone number
   */
  markAsConnected(phoneNumber: string): void {
    this.validatePhoneNumber(phoneNumber);
    
    this.phoneNumber = phoneNumber;
    this.status = 'connected';
    this.connectedAt = new Date();
    this.disconnectedAt = undefined;
    this.updatedAt = new Date();
  }

  /**
   * Mark session as disconnected
   */
  markAsDisconnected(_reason?: string): void {
    this.status = 'disconnected';
    this.disconnectedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Update authentication state
   */
  updateAuthState(newState: AuthenticationState): void {
    this.authState = newState;
    this.updatedAt = new Date();
  }

  /**
   * Update session status
   */
  updateStatus(newStatus: SessionStatus): void {
    this.status = newStatus;
    this.updatedAt = new Date();
  }

  /**
   * Check if session is expired based on TTL
   */
  isExpired(): boolean {
    if (!this.ttl) return false;
    return Date.now() / 1000 > this.ttl;
  }

  /**
   * Check if session is active (connected and not expired)
   */
  isActive(): boolean {
    return this.status === 'connected' && !this.isExpired();
  }

  /**
   * Check if session can be reconnected
   */
  canReconnect(): boolean {
    return this.authState !== null && !this.isExpired();
  }

  /**
   * Get session duration in milliseconds
   */
  getSessionDuration(): number | null {
    if (!this.connectedAt) return null;
    const endTime = this.disconnectedAt || new Date();
    return endTime.getTime() - this.connectedAt.getTime();
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      userId: this.userId,
      sessionId: this.sessionId,
      phoneNumber: this.phoneNumber,
      authState: this.authState ? JSON.stringify(this.authState) : null,
      status: this.status,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      connectedAt: this.connectedAt?.toISOString(),
      disconnectedAt: this.disconnectedAt?.toISOString(),
      ttl: this.ttl,
      deviceName: this.deviceName,
      browserName: this.browserName
    };
  }

  /**
   * Deserialize from JSON
   */
  static fromJSON(data: Record<string, any>): SessionEntity {
    return new SessionEntity(
      data['userId'],
      data['sessionId'],
      data['phoneNumber'] || null,
      data['authState'] ? JSON.parse(data['authState']) : null,
      data['status'],
      new Date(data['createdAt']),
      new Date(data['updatedAt']),
      data['connectedAt'] ? new Date(data['connectedAt']) : undefined,
      data['disconnectedAt'] ? new Date(data['disconnectedAt']) : undefined,
      data['ttl'],
      data['deviceName'],
      data['browserName']
    );
  }

  /**
   * Validate user ID format
   */
  private validateUserId(userId: string): void {
    if (!userId || typeof userId !== 'string') {
      throw new Error('User ID must be a non-empty string');
    }
    if (userId.length < 3 || userId.length > 100) {
      throw new Error('User ID must be between 3 and 100 characters');
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(userId)) {
      throw new Error('User ID can only contain alphanumeric characters, underscores, and hyphens');
    }
  }

  /**
   * Validate session ID format
   */
  private validateSessionId(sessionId: string): void {
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Session ID must be a non-empty string');
    }
    if (sessionId.length < 8) {
      throw new Error('Session ID must be at least 8 characters');
    }
  }

  /**
   * Validate phone number format
   */
  private validatePhoneNumber(phoneNumber: string): void {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      throw new Error('Phone number must be a non-empty string');
    }
    // Basic phone number validation - can be enhanced with libphonenumber-js
    if (!/^\+?[1-9]\d{1,14}$/.test(phoneNumber.replace(/\s/g, ''))) {
      throw new Error('Invalid phone number format');
    }
  }

  /**
   * Clone the entity with updated properties
   */
  clone(updates: Partial<Omit<SessionEntity, 'userId' | 'sessionId' | 'createdAt'>>): SessionEntity {
    return new SessionEntity(
      this.userId,
      this.sessionId,
      updates.phoneNumber !== undefined ? updates.phoneNumber : this.phoneNumber,
      updates.authState !== undefined ? updates.authState : this.authState,
      updates.status !== undefined ? updates.status : this.status,
      this.createdAt,
      updates.updatedAt !== undefined ? updates.updatedAt : new Date(),
      updates.connectedAt !== undefined ? updates.connectedAt : this.connectedAt,
      updates.disconnectedAt !== undefined ? updates.disconnectedAt : this.disconnectedAt,
      updates.ttl !== undefined ? updates.ttl : this.ttl,
      updates.deviceName !== undefined ? updates.deviceName : this.deviceName,
      updates.browserName !== undefined ? updates.browserName : this.browserName
    );
  }
}
