# WhatsApp API Middleware Foundation

A robust, scalable WhatsApp API middleware that bridges WhatsApp Business functionality with enterprise applications through clean REST APIs and real-time WebSocket connections. Built with Clean Architecture patterns and production-ready session management using Baileys + DynamoDB.

## ✨ Features

### 🏗️ **Foundation Architecture (Issue #100)**
- **Clean Architecture** implementation with proper layer separation
- **Domain-Driven Design** with entities, value objects, and services
- **Dependency Injection** using tsyringe for testability and modularity
- **Configuration Management** with environment-aware settings
- **Structured Logging** with contextual information and multiple formats
- **Health Monitoring** with detailed system diagnostics
- **Error Handling** with custom error types and proper HTTP responses
- **Docker Development Environment** with hot reload and debugging

### 🔐 **Session Management (Issue #102)**
- **Multi-user session isolation** with secure session storage
- **QR code generation** for WhatsApp Web authentication
- **Auto-reconnection** with exponential backoff and failure handling
- **Session lifecycle management** (create, connect, disconnect, cleanup)
- **TTL-based session expiration** with automatic cleanup
- **Session health monitoring** and diagnostics with scoring
- **Concurrent session limits** and resource management

### 📱 **WhatsApp Integration**
- **Baileys WhatsApp Web API** integration with latest version
- **Real-time connection status** tracking and event handling
- **Authentication state persistence** in DynamoDB with encryption
- **Multi-device support** with custom device/browser names
- **Connection event handling** (QR, connecting, connected, disconnected)
- **Message sending capabilities** with delivery confirmation
- **Media support** preparation for future file handling

### 🗄️ **Data Persistence & Scalability**
- **DynamoDB** for scalable session storage with global tables support
- **Optimized table design** with GSI for efficient status queries
- **Atomic operations** for session creation/updates with conflict resolution
- **Batch operations** for bulk session management and cleanup
- **TTL-based automatic cleanup** of expired sessions
- **Data encryption** at rest and in transit
- **Backup and restore** capabilities for disaster recovery

### 🔧 **Production Ready Features**
- **Comprehensive API validation** with express-validator
- **Rate limiting** and DDoS protection
- **CORS configuration** for cross-origin requests
- **Security headers** and input sanitization
- **Monitoring and alerting** integration ready
- **Horizontal scaling** support with load balancing
- **CI/CD pipeline** ready with automated testing
- **Documentation** with OpenAPI/Swagger support

## 🏗️ Architecture

This foundation implements **Clean Architecture** with:
- **Domain-driven design** with clear separation of concerns
- **Dependency Injection** via `tsyringe` for runtime flexibility and testability
- **Configuration-First** approach with environment-aware, fail-fast configuration management
- **Observable** system with structured logging and health monitoring
- **Container-Ready** Docker-first development and deployment

## 🚀 Quick Start

### Prerequisites

- Node.js >= 18.0.0
- npm >= 9.0.0
- Docker & Docker Compose

### 5-Minute Setup

```bash
# 1. Clone and navigate
git clone <repo-url>
cd tools/node/whatsapp-manager

# 2. Install dependencies
npm install

# 3. Set up environment
cp .env.example environments/.env.development.local

# 4. Start with Docker
docker-compose up -d

# 5. Verify health
curl http://localhost:3000/health
```

## 📁 Project Structure

```
src/
├── api/                    # 🌐 Express route handlers & middleware
│   ├── controllers/        # REST API controllers (session.controller.ts)
│   ├── middleware/         # Custom Express middleware (validation, rate limiting)
│   └── routes/             # Route definitions (session.routes.ts)
├── application/            # 🎯 Use cases & business logic
│   ├── services/           # Application services (health.service.ts)
│   └── use-cases/          # Business use cases (StartSession, GetStatus, etc.)
├── domain/                 # 🏛️ Core business models
│   ├── entities/           # Domain entities (SessionEntity)
│   ├── repositories/       # Repository interfaces (ISessionRepository)
│   ├── services/           # Domain services (ISessionDomainService, IWhatsAppService)
│   └── value-objects/      # Value objects (SessionId, PhoneNumber)
├── infrastructure/         # 🔧 External integrations
│   ├── database/           # DynamoDB implementations (SessionRepositoryDynamoDB)
│   └── whatsapp/           # Baileys integration (WhatsAppService, AuthStateAdapter)
├── shared/                 # 🔄 Cross-cutting concerns
│   ├── config/             # Configuration management
│   ├── logging/            # Logging infrastructure
│   ├── errors/             # Error types & handling
│   └── utils/              # Utility functions
├── di/                     # 💉 Dependency injection
│   └── container.ts        # Main DI container with all registrations
└── index.ts                # 🚀 Application bootstrap
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev                 # Start with hot reload
npm run build              # Build for production
npm run start              # Start production build

# Code Quality
npm run lint               # Run ESLint
npm run lint:fix           # Fix ESLint issues
npm run format             # Format with Prettier
npm run typecheck          # TypeScript type checking

# Testing
npm test                   # Run unit tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage
npm run test:e2e           # Run end-to-end tests

# Docker
npm run docker:dev         # Start development stack
npm run docker:build       # Build production image
```

### Environment Configuration

The application supports multiple environments with hierarchical configuration loading:

| Environment | Config File | Purpose |
|-------------|-------------|---------|
| Development | `environments/.env.development.local` | Local development |
| Test | `environments/.env.test.local` | Testing |
| UAT | Environment variables + AWS Secrets | User acceptance testing |
| Production | Environment variables + AWS Secrets | Production deployment |

### Key Configuration Variables

```bash
# Core Settings
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# AWS Configuration
AWS_REGION=ap-southeast-1
DYNAMODB_ENDPOINT=http://dynamodb-local:8000  # Local only
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev

# WhatsApp Configuration
MAX_CONCURRENT_SESSIONS=50
SESSION_TTL_HOURS=72
QR_CODE_TIMEOUT_SECONDS=60
RECONNECT_ATTEMPTS=3
RECONNECT_DELAY_MS=5000

# Security
JWT_SECRET=your-secret-key-minimum-32-characters

# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty
```

## 📡 API Endpoints

### Session Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/sessions/{userId}` | Start a new WhatsApp session |
| `POST` | `/api/sessions/{userId}/reconnect` | Reconnect existing session |
| `GET` | `/api/sessions/{userId}` | Get session status |
| `DELETE` | `/api/sessions/{userId}` | Terminate session |

### Session Listing

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/sessions` | List all sessions with pagination |
| `GET` | `/api/sessions/active` | List active sessions only |
| `GET` | `/api/sessions/statistics` | Get session statistics |

### Admin Operations

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/sessions/cleanup/expired` | Cleanup expired sessions |
| `POST` | `/api/sessions/terminate-all` | Terminate all sessions |

### Example Usage

```bash
# Start a session
curl -X POST http://localhost:3000/api/sessions/user123 \
  -H "Content-Type: application/json" \
  -d '{"deviceName": "My Device", "browserName": "Chrome"}'

# Get session status
curl http://localhost:3000/api/sessions/user123

# List all sessions
curl http://localhost:3000/api/sessions?limit=10&status=connected

# Terminate session
curl -X DELETE http://localhost:3000/api/sessions/user123 \
  -H "Content-Type: application/json" \
  -d '{"reason": "Manual termination"}'
```

### Postman Collection

Import the complete API collection from `docs/WhatsApp-Manager-API.postman_collection.json` for interactive testing.

## 🏥 Health Monitoring

The application provides comprehensive health monitoring:

### Health Endpoint

```bash
GET /health
GET /api/health
```

**Response:**
```json
{
  "status": "ok",
  "environment": "development",
  "version": "1.0.0",
  "uptime": 123,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "checks": {
    "database": {
      "status": "ok",
      "message": "Database configuration valid",
      "responseTime": 5,
      "details": {
        "tableName": "WhatsAppSessions-dev",
        "region": "ap-southeast-1",
        "endpoint": "http://dynamodb-local:8000"
      }
    },
    "memory": {
      "status": "ok",
      "message": "Memory usage normal",
      "details": {
        "rss": "45MB",
        "heapUsed": "23MB",
        "heapTotal": "35MB",
        "heapUsagePercent": "66%"
      }
    },
    "dependencies": []
  }
}
```

## 🧪 Testing

### Test Structure

```
tests/
├── unit/                   # Unit tests
├── integration/            # Integration tests
└── e2e/                    # End-to-end tests
```

### Running Tests

```bash
# Unit tests
npm test

# With coverage
npm run test:coverage

# E2E tests
npm run test:e2e

# Watch mode
npm run test:watch
```

## 🐳 Docker Development

### Development Stack

```bash
# Start complete development environment
docker-compose up -d

# View logs
docker-compose logs -f whatsapp-manager

# Stop stack
docker-compose down
```

The development stack includes:
- **WhatsApp Manager** (port 3000)
- **DynamoDB Local** (port 8000)

### Production Build

```bash
# Build production image
docker build -f docker/Dockerfile -t whatsapp-api .

# Run production container
docker run -p 3000:3000 --env-file .env.production whatsapp-api
```

## 📊 Logging

Structured logging with **Pino**:

```typescript
// Automatic request correlation
logger.info('Processing request', { userId: '123', operation: 'sendMessage' });

// Child loggers
const requestLogger = logger.withRequestId('req-123');
requestLogger.debug('Validation passed');

// Error logging
logger.error('Database connection failed', { error, retryCount: 3 });
```

## 🔒 Security Features

- **Helmet.js** for security headers
- **CORS** configuration
- **Rate limiting** (configurable per environment)
- **Input validation** with Joi schemas
- **JWT** authentication ready
- **Non-root Docker** containers

## 🚦 Validation Criteria

The foundation passes all validation criteria:

✅ `docker-compose up` starts complete development stack in <30 seconds
✅ Health endpoint returns environment-specific status with uptime
✅ Configuration validation prevents startup with missing critical variables
✅ Structured logging works across all environments
✅ TypeScript compilation produces zero errors and warnings
✅ ESLint passes with zero violations
✅ Prettier formatting is consistent

### Validation Commands

```bash
# These commands must succeed:
docker-compose up -d                    # Stack starts clean
curl localhost:3000/health              # Returns 200 + JSON status
npm run lint                           # Zero violations
npm run build                          # Clean TypeScript build
npm run test                           # All tests pass

# This command must fail gracefully:
JWT_SECRET= npm run start:prod         # Exits with code 1, logs error
```

## 🔄 Next Steps

This foundation enables rapid development of:

1. **Authentication & QR Code Management** (Issue #02)
2. **Baileys WhatsApp Integration** (Issue #03)
3. **Message API Endpoints** (Issue #04)
4. **Web Dashboard** (Issue #05)

## 📝 Contributing

1. Follow the established architecture patterns
2. Add tests for new functionality
3. Update documentation
4. Ensure all validation criteria pass

## 📄 License

MIT License - see LICENSE file for details.