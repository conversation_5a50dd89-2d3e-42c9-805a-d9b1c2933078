import { Router } from 'express';
import { DIContainer } from '../../di/container';
import { SessionController } from '../controllers/session.controller';
import { validateSessionRequest } from '../middleware/validation.middleware';
import { rateLimitMiddleware as rateLimitMiddlewareImpl } from '../middleware/rate-limit.middleware';

/**
 * Create session routes
 */
export function createSessionRoutes(): Router {
  const router = Router();
  const sessionController = DIContainer.resolve<SessionController>('SessionController');

  // Apply rate limiting to all session routes
  router.use(rateLimitMiddlewareImpl({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 requests per minute per IP
    message: 'Too many session requests, please try again later'
  }));

  /**
   * GET /api/sessions
   * List all sessions with optional filtering
   */
  router.get('/', 
    validateSessionRequest.listSessions,
    sessionController.listSessions.bind(sessionController)
  );

  /**
   * GET /api/sessions/statistics
   * Get session statistics
   */
  router.get('/statistics',
    sessionController.getSessionStatistics.bind(sessionController)
  );

  /**
   * GET /api/sessions/active
   * Get active sessions only
   */
  router.get('/active',
    sessionController.getActiveSessions.bind(sessionController)
  );

  /**
   * POST /api/sessions/cleanup/expired
   * Cleanup expired sessions (admin endpoint)
   */
  router.post('/cleanup/expired',
    rateLimitMiddlewareImpl({
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 5, // 5 requests per 5 minutes
      message: 'Cleanup operations are rate limited'
    }),
    sessionController.cleanupExpiredSessions.bind(sessionController)
  );

  /**
   * POST /api/sessions/terminate-all
   * Terminate all sessions (admin endpoint)
   */
  router.post('/terminate-all',
    rateLimitMiddlewareImpl({
      windowMs: 10 * 60 * 1000, // 10 minutes
      maxRequests: 2, // 2 requests per 10 minutes
      message: 'Terminate all operations are heavily rate limited'
    }),
    validateSessionRequest.terminateAll,
    sessionController.terminateAllSessions.bind(sessionController)
  );

  /**
   * POST /api/sessions/:userId
   * Start a new WhatsApp session
   */
  router.post('/:userId',
    validateSessionRequest.startSession,
    rateLimitMiddlewareImpl({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5, // 5 session starts per minute per IP
      message: 'Too many session start requests'
    }),
    sessionController.startSession.bind(sessionController)
  );

  /**
   * POST /api/sessions/:userId/reconnect
   * Reconnect an existing session
   */
  router.post('/:userId/reconnect',
    validateSessionRequest.reconnectSession,
    rateLimitMiddlewareImpl({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 reconnect attempts per minute
      message: 'Too many reconnection requests'
    }),
    sessionController.reconnectSession.bind(sessionController)
  );

  /**
   * GET /api/sessions/:userId
   * Get session status
   */
  router.get('/:userId',
    validateSessionRequest.getSessionStatus,
    sessionController.getSessionStatus.bind(sessionController)
  );

  /**
   * DELETE /api/sessions/:userId
   * Terminate session
   */
  router.delete('/:userId',
    validateSessionRequest.terminateSession,
    rateLimitMiddlewareImpl({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 terminations per minute
      message: 'Too many termination requests'
    }),
    sessionController.terminateSession.bind(sessionController)
  );

  return router;
}

/**
 * Session-specific rate limiting middleware factory
 */
// Removed duplicate rate limit middleware - using imported one
