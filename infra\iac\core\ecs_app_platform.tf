# ECS App Platform for ${var.api_subdomain}.${var.env}.${var.domain_name}
# Network configuration is managed in network.tf

# Load services configuration from YAML
# Try to load from calling module's configs first, then fall back to core configs
locals {
  services_config = yamldecode(
    fileexists("${path.root}/configs/ecs_services.yaml") ?
    file("${path.root}/configs/ecs_services.yaml") :
    file("${path.module}/configs/ecs_services.yaml")
  )
}

# Using the ALB certificate defined in certificates.tf

# Create the ECS App Platform
module "api_platform" {
  source   = "../../modules/ecs_app_platform"
  env      = var.env
  app_name = "api"

  # Use network foundation outputs
  vpc_id             = module.network.vpc_id
  alb_subnet_ids     = module.network.public_subnet_ids
  service_subnet_ids = var.use_private_subnets ? module.network.private_subnet_ids : module.network.public_subnet_ids

  # Use services configuration from YAML
  services_config = local.services_config

  # HTTPS Configuration
  create_https_listener = true
  certificate_arn       = module.alb_certificate.certificate_arn
  ssl_policy            = "ELBSecurityPolicy-TLS13-1-2-2021-06"

  # HTTP to HTTPS redirect
  create_http_listener = true
  http_listener_type   = "redirect"

  # Default settings for all services
  # Private subnets don't need public IP if NAT Gateway is enabled
  default_assign_public_ip = var.use_private_subnets ? false : true

  task_role_policy_arns = [aws_iam_policy.ecs_tasks_secrets_read.arn]

  tags = merge(local.this.tags, {
    Component = "ECS App Platform"
  })

  depends_on = [module.alb_certificate]
}

# Generate unique suffix for IAM resources
resource "random_id" "ecs_unique_suffix" {
  byte_length = 2
}

# Read-only access to Secrets Manager for ECS tasks
resource "aws_iam_policy" "ecs_tasks_secrets_read" {
  name        = "${var.env}-ecs-tasks-secrets-read-${random_id.ecs_unique_suffix.hex}"
  description = "Allow ECS tasks in ${var.api_subdomain}.${var.env}.${var.domain_name} to read secrets"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "SecretsRead",
        Effect = "Allow",
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ],
        Resource = "arn:aws:secretsmanager:ap-southeast-5:*:secret:*"
      }
    ]
  })
}

output "alb_dns_name" {
  value       = module.api_platform.alb_dns_name
  description = "The DNS name of the ALB"
}

# ECS Outputs
output "ecs_cluster_name" {
  value       = module.api_platform.cluster_name
  description = "The name of the ECS cluster"
}

output "services" {
  value       = module.api_platform.services
  description = "The ECS services"
}
