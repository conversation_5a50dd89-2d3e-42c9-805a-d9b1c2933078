[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --color=yes
    --durations=10
asyncio_mode = auto
markers =
    unit: Unit tests (fast, isolated, no external dependencies)
    integration: Integration tests (slower, may use real services)
    slow: Slow running tests
    aws: Tests that require AWS credentials
    openai: Tests that require OpenAI API key
    supabase: Tests that require Supabase connection
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
minversion = 6.0