import { SessionEntity, SessionStatus, AuthenticationState } from '../entities/SessionEntity';

/**
 * Session repository interface
 * Defines all data access operations for session management
 */
export interface ISessionRepository {
  // Session creation (uses CreateItem - fails if session already exists)
  createSession(session: SessionEntity): Promise<void>;
  
  // Session updates (uses UpdateItem with condition expressions)
  updateSessionStatus(
    userId: string, 
    status: SessionStatus, 
    metadata?: Partial<SessionEntity>
  ): Promise<void>;
  
  updateAuthState(userId: string, authState: AuthenticationState): Promise<void>;
  
  updateConnectionInfo(
    userId: string, 
    phoneNumber: string, 
    connectedAt: Date
  ): Promise<void>;
  
  // Session retrieval
  findByUserId(userId: string): Promise<SessionEntity | null>;
  findBySessionId(sessionId: string): Promise<SessionEntity | null>;
  
  // Session management
  upsertSession(session: SessionEntity): Promise<void>; // Uses PutItem when updates are acceptable
  delete(userId: string): Promise<void>;
  deleteBySessionId(sessionId: string): Promise<void>;
  
  // Conditional operations
  createIfNotExists(session: SessionEntity): Promise<{ created: boolean; existing?: SessionEntity }>;
  updateIfExists(userId: string, updates: Partial<SessionEntity>): Promise<boolean>;
  
  // Query operations
  findAllActive(): Promise<SessionEntity[]>;
  findByStatus(status: SessionStatus): Promise<SessionEntity[]>;
  findExpiredSessions(): Promise<SessionEntity[]>;
  
  // Batch operations
  createMany(sessions: SessionEntity[]): Promise<void>;
  deleteMany(userIds: string[]): Promise<void>;
  
  // Monitoring
  count(): Promise<number>;
  countByStatus(status: SessionStatus): Promise<number>;
}

/**
 * Custom error classes for repository operations
 */
export class SessionAlreadyExistsError extends Error {
  override name = 'SessionAlreadyExistsError';
  constructor(message: string) {
    super(message);
  }
}

export class SessionNotFoundError extends Error {
  override name = 'SessionNotFoundError';
  constructor(message: string) {
    super(message);
  }
}

export class DatabaseError extends Error {
  override name = 'DatabaseError';
  constructor(message: string, public override readonly cause?: Error) {
    super(message);
  }
}

/**
 * Repository query options
 */
export interface QueryOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'connectedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Paginated query result
 */
export interface PaginatedResult<T> {
  items: T[];
  totalCount: number;
  hasMore: boolean;
  nextToken?: string;
}

/**
 * Session statistics interface
 */
export interface SessionStatistics {
  totalSessions: number;
  activeSessions: number;
  connectedSessions: number;
  qrPendingSessions: number;
  disconnectedSessions: number;
  errorSessions: number;
  averageSessionDuration: number; // in milliseconds
  oldestActiveSession?: Date;
  newestSession?: Date;
}

/**
 * Extended session repository interface with advanced features
 */
export interface IAdvancedSessionRepository extends ISessionRepository {
  // Paginated queries
  findAllPaginated(options?: QueryOptions): Promise<PaginatedResult<SessionEntity>>;
  findByStatusPaginated(
    status: SessionStatus, 
    options?: QueryOptions
  ): Promise<PaginatedResult<SessionEntity>>;
  
  // Statistics and monitoring
  getStatistics(): Promise<SessionStatistics>;
  getSessionsByDateRange(startDate: Date, endDate: Date): Promise<SessionEntity[]>;
  
  // Cleanup operations
  cleanupExpiredSessions(): Promise<number>;
  cleanupDisconnectedSessions(olderThanHours: number): Promise<number>;
  
  // Health checks
  healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details?: string }>;
  
  // Backup and restore
  exportSessions(userIds?: string[]): Promise<SessionEntity[]>;
  importSessions(sessions: SessionEntity[], overwrite?: boolean): Promise<number>;
}
