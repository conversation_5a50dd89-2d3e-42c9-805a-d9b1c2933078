# Docker Environment Configuration
# Used by docker-compose for container environment variables

NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# AWS Configuration
AWS_REGION=ap-southeast-1

# DynamoDB Local (Docker service name)
DYNAMODB_ENDPOINT=http://dynamodb-local:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev

# JWT Secret for Docker development
JWT_SECRET=arn:aws:secretsmanager:ap-southeast-5:611032973328:secret:uat/whatsapp-manager/jwt_token
QR_TOKEN_EXPIRY_SEC=300

# CORS - Allow all origins in Docker development
CORS_ORIGINS=*

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Development features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
