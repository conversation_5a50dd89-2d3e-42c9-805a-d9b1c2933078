# Development Environment Configuration

# Application
NODE_ENV=development
PORT=3000
API_PREFIX=/api

# JWT Configuration
JWT_SECRET=development-secret-key-123-minimum-32-characters-required-for-security
JWT_EXPIRES_IN=24h

# Database Configuration
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev
AWS_REGION=ap-southeast-1
DYNAMODB_ENDPOINT=http://localhost:8000

# WhatsApp Configuration
MAX_CONCURRENT_SESSIONS=50
SESSION_TTL_HOURS=72
QR_CODE_TIMEOUT_SECONDS=60
RECONNECT_ATTEMPTS=3
RECONNECT_DELAY_MS=5000

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Development Features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
