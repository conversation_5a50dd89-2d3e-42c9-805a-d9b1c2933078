/**
 * Dependency injection tokens for type-safe service resolution
 */

export const DI_TOKENS = {
  // Core services
  IConfigService: Symbol.for('IConfigService'),
  ILoggerService: Symbol.for('ILoggerService'),
  
  // Application services
  IHealthService: Symbol.for('IHealthService'),
  
  // Infrastructure services
  IDatabaseService: Symbol.for('IDatabaseService'),
  
  // Controllers
  HealthController: Symbol.for('HealthController'),
  
  // Middleware
  ErrorHandler: Symbol.for('ErrorHandler'),
} as const;

export type DITokens = typeof DI_TOKENS;
