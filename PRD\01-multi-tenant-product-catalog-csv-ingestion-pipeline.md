# Multi-Tenant Product Catalog CSV Ingestion Pipeline

## Product Requirements Document (PRD)

**Epic:** EzyChat Multi-Tenant RAG MVP Implementation
**Component:** Backend/Data Pipeline
**Priority:** High
**Labels:** `multi-tenant`, `rag`, `vector-embeddings`, `aws-lambda`, `python3.12`, `backend`, `clean-architecture`

---

## Executive Summary

Implement a serverless, multi-tenant CSV product catalog ingestion pipeline that enables businesses to upload product catalogs with complete tenant isolation, automatic vector embedding generation, and seamless integration with the EzyChat RAG-powered WhatsApp sales assistant.

## Business Objectives

- **Multi-Tenant Support**: Enable independent product catalog management for multiple business tenants
- **Data Isolation**: Ensure complete separation of tenant data throughout the pipeline
- **Scalability**: Support high-volume product catalog processing (10,000+ products per tenant)
- **Integration**: Seamless connection with existing WhatsApp chatbot RAG system

## Technical Architecture

### System Overview
**Workflow**: S3 Upload (tenant-scoped b prefix) → Lambda Trigger → CSV Processing → Supabase pgvector Storage

### Technology Stack
- **Runtime Environment**: Python 3.12
- **Architecture Pattern**: Clean Architecture with dependency injection
- **Containerization**: Docker with AWS Lambda base image
- **Infrastructure as Code**: Terraform for AWS resource management
- **Vector Database**: Supabase with pgvector extension and Row Level Security (RLS)
- **Object Storage**: AWS S3 with Lambda function triggers
- **Embeddings**: OpenAI text-embedding-3-small model
- **CI/CD**: Integrated pipeline with comprehensive testing

### Clean Architecture Structure
```
src/
├── domain/           # Business entities and rules
│   ├── entities.py   # Document, ProductData, EmbeddingVector
│   ├── interfaces.py # Repository and service interfaces
│   └── services.py   # Domain services
├── application/      # Use cases and orchestration
│   └── use_cases.py  # ProcessDocumentUseCase
├── infrastructure/   # External dependencies
│   ├── configuration.py
│   ├── logging.py
│   ├── metrics.py
│   └── dependency_injection.py
└── presentation/     # Controllers and handlers
    └── lambda_handler.py
```

### CSV Processing Workflow
**File Lifecycle Management**:
- **Upload Path**: `s3://bucket/{user_id}/uploaded/filename.csv`
- **Processing Path**: `s3://bucket/{user_id}/processing/filename.csv`
- **Completion Path**: `s3://bucket/{user_id}/completed/filename.csv`
- **Trigger**: S3 object creation/upload events automatically invoke Lambda function

---

## Functional Requirements

### Core Pipeline Features
- **F1**: Automatic processing trigger when CSV files are uploaded to `s3://bucket/{user_id}/uploaded/`
- **F2**: Multi-tenant data storage in Supabase with complete tenant isolation using `user_id` partitioning
- **F3**: Vector embedding generation using OpenAI text-embedding-3-small with configurable dimensions
- **F4**: Vector storage in Supabase pgvector with tenant metadata for filtered searches
- **F5**: Real-time processing status tracking accessible per tenant
- **F6**: Complete data isolation ensuring tenants cannot access other tenants' data

### CSV Processing Capabilities
- **F7**: Support for standard CSV format with configurable column mapping
- **F8**: Data validation and cleaning with quality scoring
- **F9**: Batch processing for large files (1000+ rows) with memory optimization
- **F10**: Error handling with detailed logging and rollback mechanisms

## Non-Functional Requirements

### Performance Requirements
- **P1**: Process 10,000+ products per tenant within 5 minutes
- **P2**: Lambda function execution within 15-minute AWS timeout limit
- **P3**: Memory utilization under 3008MB Lambda constraint
- **P4**: Vector search latency under 100ms for product queries

### Security & Compliance Requirements
- **S1**: Tenant ID extraction and validation from S3 object keys
- **S2**: Row Level Security (RLS) implementation in Supabase
- **S3**: Comprehensive audit logging for all tenant data operations
- **S4**: Secure handling of OpenAI API keys and database credentials

### Scalability & Reliability Requirements
- **R1**: Horizontal scaling capability for concurrent tenant processing
- **R2**: Retry mechanisms for transient failures
- **R3**: Monitoring and alerting for pipeline health
- **R4**: Graceful degradation under high load conditions

---

## Engineering Standards

### Clean Architecture Implementation
The system follows Clean Architecture principles with clear separation of concerns:

1. **Domain Layer**: Business entities, rules, and interfaces independent of external dependencies
2. **Application Layer**: Use cases and orchestration logic coordinating domain operations
3. **Infrastructure Layer**: External service adapters (Supabase, S3, OpenAI) implementing domain interfaces
4. **Presentation Layer**: Lambda handlers and API controllers managing external requests

### Dependency Injection & Abstraction
- **Vector Database Provider Abstraction**: Interface-based design allowing easy switching between vector storage providers
- **Configuration Management**: Environment-based configuration with comprehensive .env.example
- **Service Interfaces**: Clear contracts between layers enabling independent testing and development

### Testing Strategy
- **Unit Testing**: Comprehensive coverage (>90%) for domain and application layers
- **Integration Testing**: End-to-end pipeline testing with real AWS services
- **Contract Testing**: Interface compliance verification between layers
- **Performance Testing**: Load testing for concurrent tenant processing scenarios

### Error Handling & Monitoring
- **Structured Logging**: JSON-formatted logs with tenant context for CloudWatch integration
- **Error Classification**: Categorized error handling with appropriate retry strategies
- **Metrics Collection**: Performance and business metrics for monitoring and alerting
- **Audit Trail**: Complete logging of tenant data operations for compliance

---

## Implementation Roadmap

### Phase 1: Core Infrastructure (Weeks 1-2)
- **Infrastructure Setup**: Terraform configuration for AWS resources (S3, Lambda, IAM)
- **Supabase Configuration**: Database schema, RLS policies, pgvector extension setup
- **Domain Layer**: Core entities (Document, ProductData, EmbeddingVector) and interfaces
- **Basic Lambda Handler**: S3 event processing and tenant ID extraction

### Phase 2: Processing Pipeline (Weeks 2-3)
- **CSV Processing**: Pandas-based parsing, validation, and data cleaning
- **Vector Embeddings**: OpenAI API integration with batch processing optimization
- **Data Storage**: Supabase repository implementation with tenant isolation
- **Error Handling**: Comprehensive error classification and retry mechanisms

### Phase 3: Testing & Optimization (Week 4)
- **Unit Testing**: Domain and application layer test coverage (>90%)
- **Integration Testing**: End-to-end pipeline testing with real AWS services
- **Performance Optimization**: Memory usage optimization and batch size tuning
- **Monitoring Setup**: CloudWatch metrics, alarms, and structured logging

### Phase 4: CI/CD & Deployment (Week 4)
- **CI/CD Pipeline**: Automated testing, building, and deployment
- **Environment Configuration**: Development, staging, and production environments
- **Documentation**: API documentation, deployment guides, and troubleshooting
- **Security Review**: Code security scan and compliance verification

## Success Criteria

### Technical Metrics
- **Processing Performance**: <5 minutes for 10,000 products per tenant
- **System Reliability**: >99% success rate for valid CSV files
- **Resource Efficiency**: <90% Lambda memory utilization
- **Code Quality**: >90% test coverage, <10 cyclomatic complexity per method

### Business Metrics
- **Multi-Tenant Isolation**: Zero cross-tenant data contamination incidents
- **Scalability**: Support for concurrent processing of multiple tenants
- **Integration Success**: Seamless connection with existing WhatsApp chatbot system
- **Operational Excellence**: Comprehensive monitoring and alerting capabilities

---

## Risk Assessment & Mitigation

### Technical Risks
- **Lambda Timeout Risk**: Large CSV files may exceed 15-minute Lambda limit
  - *Mitigation*: Implement chunked processing with SQS for large files
- **Memory Constraints**: Vector embedding generation may consume excessive memory
  - *Mitigation*: Batch processing optimization and memory profiling
- **API Rate Limits**: OpenAI API throttling during high-volume processing
  - *Mitigation*: Exponential backoff retry strategy and request batching

### Operational Risks
- **Multi-Tenant Data Leakage**: Potential cross-tenant data contamination
  - *Mitigation*: Comprehensive RLS policies and tenant ID validation
- **Cost Escalation**: High OpenAI API usage costs
  - *Mitigation*: Embedding caching and cost monitoring alerts
- **Scalability Bottlenecks**: Concurrent processing limitations
  - *Mitigation*: Horizontal scaling design and load testing

## Dependencies & Assumptions

### External Dependencies
- **AWS Services**: S3, Lambda, CloudWatch availability and performance
- **Supabase**: Database uptime and pgvector extension stability
- **OpenAI API**: Service availability and consistent response times
- **Terraform**: Infrastructure provisioning and state management

### Key Assumptions
- **CSV Format Standardization**: Tenants will provide consistently formatted CSV files
- **Network Connectivity**: Reliable internet connectivity for API calls
- **Resource Limits**: Current AWS Lambda limits remain sufficient for processing requirements
- **Data Volume**: Product catalog sizes remain within projected ranges (10,000 products max)

---

## Appendix

### Glossary
- **Clean Architecture**: Software design philosophy emphasizing separation of concerns and dependency inversion
- **Multi-Tenancy**: Software architecture where a single instance serves multiple customers (tenants)
- **Vector Embeddings**: Numerical representations of text data for semantic similarity search
- **Row Level Security (RLS)**: Database security feature controlling access to table rows based on user context
- **pgvector**: PostgreSQL extension for vector similarity search operations

### Related Documentation
- [EzyChat WhatsApp Integration Specification](../whatsapp-integration/README.md)
- [Supabase Multi-Tenant Setup Guide](../infrastructure/supabase-setup.md)
- [AWS Lambda Deployment Guide](../deployment/lambda-deployment.md)
- [Vector Embedding Best Practices](../ai/embedding-guidelines.md)

---

**Document Version**: 2.0
**Last Updated**: 2025-08-08
**Next Review**: 2025-08-15
**Approval Status**: Ready for Implementation