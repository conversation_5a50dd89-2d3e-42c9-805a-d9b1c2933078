import { Router } from 'express';
import { DIContainer } from '../../di/container';
import { HealthController } from '../controllers/health.controller';

/**
 * Health check routes
 */
export function createHealthRoutes(): Router {
  const router = Router();
  const healthController = DIContainer.resolve<HealthController>('HealthController');

  // GET /health - Health check endpoint
  router.get('/', (req, res, next) => {
    healthController.getHealth(req, res, next).catch(next);
  });

  return router;
}
