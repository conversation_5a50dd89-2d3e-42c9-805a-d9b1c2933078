name: App - Node.js Lambda Sample CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'sample/lambda-nodejs/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'sample/lambda-nodejs/**'
  workflow_dispatch:

# 🔧 PROJECT CONFIGURATION
# For new Lambda functions, modify these 4 values:
# 1. lambda-path: Directory containing your Lambda code
# 2. function-name: Base name for Docker image
# 3. uat-function-name: UAT Lambda function name
# 4. prod-function-name: Production Lambda function name
#
# Infrastructure settings (AWS accounts, regions, roles) are in repository variables

jobs:
  # Test and Build
  test-build:
    uses: ./.github/workflows/reusable-nodejs-test-build.yml
    with:
      lambda-path: sample/lambda-nodejs          # 🔧 MODIFY: Path to Lambda code
      node-version: '18'
      run-tests: true
      run-lint: true
      run-build: true

  # Build and Push Docker Image
  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: sample/lambda-nodejs          # 🔧 MODIFY: Path to Lambda code
      function-name: lambda-nodejs               # 🔧 MODIFY: Base function name
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  # Deploy to UAT (automatic)
  deploy-uat:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: uat-lambda-api-sample-lambda # 🔧 MODIFY: UAT function name
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: UAT
      test-payload: '{"path":"/sample","httpMethod":"GET"}'

  # Deploy to Production (manual trigger only)
  deploy-prod:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: [docker-build, deploy-uat]
    if: github.ref == 'refs/heads/main' && github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: prod-lambda-api-sample-lambda # 🔧 MODIFY: Production function name
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.PROD_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: Production
      test-payload: '{"path":"/sample","httpMethod":"GET"}'

  # Deployment Summary
  summary:
    runs-on: ubuntu-latest
    needs: [docker-build, deploy-uat, deploy-prod]
    if: always() && (needs.deploy-uat.result == 'success' || needs.deploy-prod.result == 'success')
    steps:
      - name: Deployment Summary
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Docker Image**: ${{ needs.docker-build.outputs.image-uri }}" >> $GITHUB_STEP_SUMMARY
          echo "- **UAT**: ${{ needs.deploy-uat.result == 'success' && '✅ Deployed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Production**: ${{ needs.deploy-prod.result == 'success' && '✅ Deployed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY


