name: Reusable - Python Test and Build

on:
  workflow_call:
    inputs:
      lambda-path:
        required: true
        type: string
        description: 'Path to Lambda function directory'
      python-version:
        required: false
        type: string
        description: 'Python version to use'
        default: '3.12'
      cache-dependency-path:
        required: false
        type: string
        description: 'Path to requirements.txt for caching'
        default: '**/requirements.txt'
      run-tests:
        required: false
        type: boolean
        description: 'Whether to run tests'
        default: true
      run-lint:
        required: false
        type: boolean
        description: 'Whether to run linting'
        default: true
      run-build:
        required: false
        type: boolean
        description: 'Whether to run build'
        default: true
      test-command:
        required: false
        type: string
        description: 'Custom test command'
        default: 'python -m pytest'
      lint-command:
        required: false
        type: string
        description: 'Custom lint command'
        default: 'python -m flake8 . --select=E999,F821,F822,F823,F831,F841,F901'
      build-command:
        required: false
        type: string
        description: 'Custom build command'
        default: 'echo "No build step required for Python Lambda"'
    outputs:
      test-result:
        description: 'Test execution result'
        value: ${{ jobs.test-build.outputs.test-result }}
      lint-result:
        description: 'Lint execution result'
        value: ${{ jobs.test-build.outputs.lint-result }}
      build-result:
        description: 'Build execution result'
        value: ${{ jobs.test-build.outputs.build-result }}

jobs:
  test-build:
    runs-on: ubuntu-latest
    outputs:
      test-result: ${{ steps.test-status.outputs.result }}
      lint-result: ${{ steps.lint-status.outputs.result }}
      build-result: ${{ steps.build-status.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ inputs.python-version }}

      - name: Install dependencies
        run: |
          cd ${{ inputs.lambda-path }}
          echo "📦 Installing dependencies..."
          if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt
          else
            echo "⚠️ No requirements.txt found"
          fi

      - name: Run tests
        if: inputs.run-tests
        id: run-tests
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🧪 Running tests..."
          if [ -f "requirements.txt" ] && grep -q "pytest" requirements.txt; then
            ${{ inputs.test-command }}
            echo "✅ Tests passed"
          else
            echo "⚠️ No pytest found in requirements.txt, skipping tests"
          fi

      - name: Set test status
        id: test-status
        run: |
          if [ "${{ inputs.run-tests }}" == "true" ]; then
            if [ "${{ steps.run-tests.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Run linting
        if: inputs.run-lint
        id: run-lint
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🔍 Running linting..."
          if [ -f "requirements.txt" ] && grep -q "flake8" requirements.txt; then
            ${{ inputs.lint-command }}
            echo "✅ Linting passed"
          else
            echo "⚠️ No flake8 found in requirements.txt, skipping linting"
          fi

      - name: Set lint status
        id: lint-status
        run: |
          if [ "${{ inputs.run-lint }}" == "true" ]; then
            if [ "${{ steps.run-lint.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Run build
        if: inputs.run-build
        id: run-build
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🏗️ Running build..."
          ${{ inputs.build-command }}
          echo "✅ Build completed"

      - name: Set build status
        id: build-status
        run: |
          if [ "${{ inputs.run-build }}" == "true" ]; then
            if [ "${{ steps.run-build.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Summary
        run: |
          echo "## 🧪 Python Test & Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Python Version**: ${{ inputs.python-version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Lambda Path**: ${{ inputs.lambda-path }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tests**: ${{ steps.test-status.outputs.result == 'success' && '✅ Passed' || steps.test-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Linting**: ${{ steps.lint-status.outputs.result == 'success' && '✅ Passed' || steps.lint-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Build**: ${{ steps.build-status.outputs.result == 'success' && '✅ Passed' || steps.build-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY