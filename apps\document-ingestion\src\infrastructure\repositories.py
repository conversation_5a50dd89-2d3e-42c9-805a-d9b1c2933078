"""
Repository Implementations for Document Ingestion Pipeline

This module provides concrete implementations of repository interfaces
using Supabase as the persistence layer.
"""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..domain.entities import (
    Document, DocumentId, UserId, ProductEmbedding, S3Location,
    DocumentStatus, ProcessingStage
)
from ..domain.interfaces import IDocumentRepository, IProductEmbeddingRepository, ILogger, IConfiguration


class SupabaseDocumentRepository(IDocumentRepository):
    """Supabase implementation of document repository"""
    
    def __init__(self, config: IConfiguration, logger: ILogger):
        self._config = config
        self._logger = logger
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Supabase client"""
        try:
            from supabase import create_client
            self._client = create_client(
                self._config.get_supabase_url(),
                self._config.get_supabase_service_key()
            )
        except Exception as e:
            self._logger.error(f"Failed to initialize Supabase client: {e}", error=e)
            raise
    
    async def save(self, document: Document) -> None:
        """Save or update a document"""
        try:
            document_data = {
                'user_id': str(document.user_id),
                'document_id': str(document.id),
                's3_bucket': document.s3_location.bucket,
                's3_key': document.s3_location.key,
                'original_filename': document.original_filename,
                'upload_status': document.status.value,
                'processing_stage': document.processing_stage.value,
                'column_names': document.column_names,
                'total_rows': document.total_rows,
                'processed_rows': document.processed_rows,
                'error_message': document.error_message,
                'created_at': document.created_at.isoformat(),
                'updated_at': document.updated_at.isoformat(),
                'processing_started_at': document.processing_started_at.isoformat() if document.processing_started_at else None,
                'processing_completed_at': document.processing_completed_at.isoformat() if document.processing_completed_at else None
            }
            
            # Try to update first, then insert if not exists
            result = self._client.table('documents').upsert(
                document_data,
                on_conflict='user_id,document_id'
            ).execute()
            
            if not result.data:
                raise Exception(f"Failed to save document: {result}")
            
            self._logger.debug(f"Saved document {document.id} for user {document.user_id}")
            
        except Exception as e:
            self._logger.error(f"Error saving document {document.id}: {e}", error=e)
            raise
    
    async def find_by_id(self, document_id: DocumentId, user_id: UserId) -> Optional[Document]:
        """Find document by ID and user ID"""
        try:
            result = self._client.table('documents').select('*').eq(
                'user_id', str(user_id)
            ).eq('document_id', str(document_id)).execute()
            
            if not result.data:
                return None
            
            data = result.data[0]
            return self._map_to_document(data)
            
        except Exception as e:
            self._logger.error(f"Error finding document {document_id}: {e}", error=e)
            raise
    
    async def find_by_user(self, user_id: UserId) -> List[Document]:
        """Find all documents for a user"""
        try:
            result = self._client.table('documents').select('*').eq(
                'user_id', str(user_id)
            ).order('created_at', desc=True).execute()
            
            documents = []
            for data in result.data:
                documents.append(self._map_to_document(data))
            
            return documents
            
        except Exception as e:
            self._logger.error(f"Error finding documents for user {user_id}: {e}", error=e)
            raise
    
    def _map_to_document(self, data: Dict[str, Any]) -> Document:
        """Map database record to Document entity"""
        return Document(
            id=DocumentId(data['document_id']),
            user_id=UserId(data['user_id']),
            s3_location=S3Location(bucket=data['s3_bucket'], key=data['s3_key']),
            original_filename=data['original_filename'],
            status=DocumentStatus(data['upload_status']),
            processing_stage=ProcessingStage(data['processing_stage']),
            column_names=data.get('column_names', []),
            total_rows=data.get('total_rows', 0),
            processed_rows=data.get('processed_rows', 0),
            error_message=data.get('error_message'),
            created_at=datetime.fromisoformat(data['created_at'].replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00')),
            processing_started_at=datetime.fromisoformat(data['processing_started_at'].replace('Z', '+00:00')) if data.get('processing_started_at') else None,
            processing_completed_at=datetime.fromisoformat(data['processing_completed_at'].replace('Z', '+00:00')) if data.get('processing_completed_at') else None
        )


class SupabaseProductEmbeddingRepository(IProductEmbeddingRepository):
    """Supabase implementation of product embedding repository"""
    
    def __init__(self, config: IConfiguration, logger: ILogger):
        self._config = config
        self._logger = logger
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Supabase client"""
        try:
            from supabase import create_client
            self._client = create_client(
                self._config.get_supabase_url(),
                self._config.get_supabase_service_key()
            )
        except Exception as e:
            self._logger.error(f"Failed to initialize Supabase client: {e}", error=e)
            raise
    
    async def save_batch(self, embeddings: List[ProductEmbedding]) -> None:
        """Save a batch of product embeddings"""
        try:
            batch_data = []
            for embedding in embeddings:
                record = {
                    'document_id': str(embedding.document_id),
                    'user_id': str(embedding.user_id),
                    'row_index': embedding.row_index,
                    'embedding_vector': embedding.embedding_vector.vector,
                    'product_data': embedding.product_data.data,
                    'searchable_text': embedding.searchable_text,
                    'created_at': embedding.created_at.isoformat()
                }
                batch_data.append(record)
            
            # Insert in batches to avoid payload limits
            batch_size = self._config.get_supabase_batch_size()
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i + batch_size]
                result = self._client.table('product_embeddings').insert(batch).execute()
                
                if not result.data:
                    raise Exception(f"Failed to insert batch {i//batch_size + 1}: {result}")
            
            self._logger.info(f"Saved {len(embeddings)} product embeddings")
            
        except Exception as e:
            self._logger.error(f"Error saving product embeddings: {e}", error=e)
            raise
    
    async def find_by_document(self, document_id: DocumentId, user_id: UserId) -> List[ProductEmbedding]:
        """Find all embeddings for a document"""
        try:
            result = self._client.table('product_embeddings').select('*').eq(
                'user_id', str(user_id)
            ).eq('document_id', str(document_id)).execute()
            
            embeddings = []
            for data in result.data:
                embeddings.append(self._map_to_embedding(data))
            
            return embeddings
            
        except Exception as e:
            self._logger.error(f"Error finding embeddings for document {document_id}: {e}", error=e)
            raise
    
    async def search_similar(self, user_id: UserId, query_vector: List[float], 
                           similarity_threshold: float, max_results: int) -> List[Dict[str, Any]]:
        """Search for similar products using vector similarity"""
        try:
            # Use Supabase RPC function for vector similarity search
            result = self._client.rpc('find_similar_products', {
                'query_embedding': query_vector,
                'target_user_id': str(user_id),
                'similarity_threshold': similarity_threshold,
                'max_results': max_results
            }).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            self._logger.error(f"Error searching similar products for user {user_id}: {e}", error=e)
            raise
    
    def _map_to_embedding(self, data: Dict[str, Any]) -> ProductEmbedding:
        """Map database record to ProductEmbedding entity"""
        from ..domain.entities import ProductData, EmbeddingVector
        
        product_data = ProductData(data['product_data'], data['row_index'])
        embedding_vector = EmbeddingVector(
            vector=data['embedding_vector'],
            dimensions=len(data['embedding_vector']),
            model="text-embedding-3-small"  # This should come from config
        )
        
        return ProductEmbedding(
            document_id=DocumentId(data['document_id']),
            user_id=UserId(data['user_id']),
            row_index=data['row_index'],
            product_data=product_data,
            searchable_text=data['searchable_text'],
            embedding_vector=embedding_vector,
            created_at=datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        )
