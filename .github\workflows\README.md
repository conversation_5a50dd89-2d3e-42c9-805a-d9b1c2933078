# CI/CD Workflows

GitHub Actions workflows for automated testing, building, and deployment of the ezychat platform.

## Overview

This directory contains GitHub Actions workflows that handle:
- Continuous Integration (CI) for code quality and testing
- Continuous Deployment (CD) for automated deployments
- Container image building and publishing
- Infrastructure deployment
- Security scanning and compliance checks

## Workflows

### 1. CI Pipeline (`ci.yml`)
**Trigger**: Push to any branch, Pull requests
**Purpose**: Code quality, testing, and validation

**Steps**:
- Checkout code
- Setup Node.js and Python environments
- Install dependencies
- Run linting (ESLint, Flake8, Black)
- Run type checking (TypeScript, mypy)
- Run unit tests with coverage
- Run integration tests
- Security vulnerability scanning
- SonarQube analysis (if configured)

### 2. Build and Deploy (`deploy.yml`)
**Trigger**: Push to main branch, Release tags
**Purpose**: Build containers and deploy to environments

**Steps**:
- Build Docker images for all services
- Push images to ECR
- Deploy to development environment
- Run smoke tests
- Deploy to staging (if main branch)
- Deploy to production (if release tag)

### 3. Infrastructure Deploy (`infrastructure.yml`)
**Trigger**: Changes to `/infra` directory
**Purpose**: Deploy infrastructure changes

**Steps**:
- Validate Terraform configurations
- Plan infrastructure changes
- Apply changes to development
- Manual approval for production
- Apply changes to production

### 4. Security Scan (`security.yml`)
**Trigger**: Scheduled (daily), Manual
**Purpose**: Security scanning and compliance

**Steps**:
- Dependency vulnerability scan
- Container image security scan
- SAST (Static Application Security Testing)
- License compliance check
- Security policy validation

### 5. Cleanup (`cleanup.yml`)
**Trigger**: Scheduled (weekly)
**Purpose**: Clean up old resources

**Steps**:
- Delete old ECR images
- Clean up old ECS task definitions
- Remove unused CloudWatch log groups
- Archive old artifacts

## Environment Configuration

### Development Environment
- **AWS Account**: Development account
- **Region**: us-east-1
- **Branch**: Any feature branch
- **Deployment**: Automatic on push
- **Approval**: Not required

### Staging Environment
- **AWS Account**: Staging account
- **Region**: us-east-1
- **Branch**: main
- **Deployment**: Automatic on main branch push
- **Approval**: Not required

### Production Environment
- **AWS Account**: Production account
- **Region**: us-east-1
- **Branch**: Release tags (v*)
- **Deployment**: Manual approval required
- **Approval**: Required from designated approvers

## Required Secrets

### GitHub Repository Secrets
```
# AWS Credentials
AWS_ACCESS_KEY_ID_DEV
AWS_SECRET_ACCESS_KEY_DEV
AWS_ACCESS_KEY_ID_STAGING
AWS_SECRET_ACCESS_KEY_STAGING
AWS_ACCESS_KEY_ID_PROD
AWS_SECRET_ACCESS_KEY_PROD

# Container Registry
ECR_REGISTRY_DEV
ECR_REGISTRY_STAGING
ECR_REGISTRY_PROD

# Application Secrets
WHATSAPP_ACCESS_TOKEN
OPENAI_API_KEY
ANTHROPIC_API_KEY
DATABASE_ENCRYPTION_KEY

# Notification
SLACK_WEBHOOK_URL
TEAMS_WEBHOOK_URL
```

### Environment Variables
```
# Common
NODE_ENV=production
LOG_LEVEL=info
AWS_REGION=us-east-1

# Database
DATABASE_URL=postgres://...
REDIS_URL=redis://...

# External Services
DIFY_WEBHOOK_SECRET=xxx
WHATSAPP_PHONE_NUMBER_ID=xxx
```

## Workflow Examples

### CI Pipeline Example
```yaml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x]
        python-version: [3.9, 3.10]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Node.js dependencies
      run: |
        npm ci
        cd apps/dify-webhook && npm ci
        cd tools/node/whatsapp-manager && npm ci
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        cd tools/python/agent-orchestrator && pip install -r requirements.txt
    
    - name: Run linting
      run: |
        npm run lint
        cd tools/python/agent-orchestrator && flake8 .
    
    - name: Run tests
      run: |
        npm test
        cd tools/python/agent-orchestrator && pytest
```

### Deployment Pipeline Example
```yaml
name: Deploy to AWS

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
        aws-region: us-east-1
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: ./apps/dify-webhook
        push: true
        tags: ${{ steps.login-ecr.outputs.registry }}/ezychat/dify-webhook:${{ github.sha }}
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.ref == 'refs/heads/main' && 'staging' || 'production' }}
    
    steps:
    - name: Deploy to ECS
      run: |
        aws ecs update-service \
          --cluster ezychat-${{ env.ENVIRONMENT }} \
          --service dify-webhook \
          --force-new-deployment
```

## Best Practices

### Security
- Use OIDC for AWS authentication when possible
- Store secrets in GitHub Secrets, not in code
- Use environment protection rules for production
- Enable branch protection rules

### Performance
- Use caching for dependencies
- Parallelize jobs where possible
- Use matrix builds for multiple versions
- Optimize Docker layer caching

### Monitoring
- Add workflow status badges to README
- Set up notifications for failed deployments
- Monitor deployment metrics
- Use step summaries for better visibility

### Testing
- Run tests in parallel
- Use test containers for integration tests
- Generate test reports and coverage
- Fail fast on critical issues

## Troubleshooting

### Common Issues

1. **Permission Errors**: Check IAM roles and policies
2. **ECR Push Failures**: Verify ECR repository exists
3. **ECS Deployment Failures**: Check service and task definition
4. **Terraform Errors**: Validate syntax and state

### Debugging Tips

```bash
# Check workflow runs
gh run list

# View workflow logs
gh run view <run-id>

# Debug failed step
gh run view <run-id> --log-failed

# Re-run failed jobs
gh run rerun <run-id>
```

## Monitoring and Notifications

### Slack Integration
```yaml
- name: Notify Slack
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    channel: '#deployments'
    webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Email Notifications
- Configure in repository settings
- Set up for deployment failures
- Include relevant team members

## Contributing

1. Create feature branch for workflow changes
2. Test changes in development environment
3. Create pull request with detailed description
4. Peer review required for production workflows
5. Monitor first few runs after deployment