import 'reflect-metadata';
import { container } from 'tsyringe';

// Core services
import { ConfigService } from '../shared/config/config.service';
import { LoggerService } from '../shared/logging/logger.service';
import { HealthService } from '../application/services/health.service';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../shared/errors/error-handler';

// Domain services
import { SessionDomainService } from '../domain/services/SessionDomainService';

// Infrastructure services
import { SessionRepositoryDynamoDB } from '../infrastructure/database/SessionRepositoryDynamoDB';
import { BaileysAuthStateAdapter } from '../infrastructure/whatsapp/BaileysAuthStateAdapter';
import { WhatsAppService } from '../infrastructure/whatsapp/WhatsAppService';

// Use cases
import { StartSessionUseCase } from '../application/use-cases/StartSessionUseCase';
import { GetSessionStatusUseCase } from '../application/use-cases/GetSessionStatusUseCase';
import { TerminateSessionUseCase } from '../application/use-cases/TerminateSessionUseCase';
import { ListSessionsUseCase } from '../application/use-cases/ListSessionsUseCase';

// Controllers
import { HealthController } from '../api/controllers/health.controller';
import { SessionController } from '../api/controllers/session.controller';

/**
 * Dependency injection container setup
 * Registers all services and their dependencies
 */
export class DIContainer {
  static initialize(): void {
    // Register core services
    container.registerSingleton('IConfigService', ConfigService);
    container.registerSingleton('ILoggerService', LoggerService);

    // Register domain services
    container.registerSingleton('ISessionDomainService', SessionDomainService);

    // Register infrastructure services
    container.registerSingleton('ISessionRepository', SessionRepositoryDynamoDB);
    container.registerSingleton('BaileysAuthStateAdapter', BaileysAuthStateAdapter);
    container.registerSingleton('IWhatsAppService', WhatsAppService);

    // Register use cases
    container.registerSingleton('StartSessionUseCase', StartSessionUseCase);
    container.registerSingleton('GetSessionStatusUseCase', GetSessionStatusUseCase);
    container.registerSingleton('TerminateSessionUseCase', TerminateSessionUseCase);
    container.registerSingleton('ListSessionsUseCase', ListSessionsUseCase);

    // Register application services
    container.registerSingleton('IHealthService', HealthService);

    // Register middleware
    container.registerSingleton('ErrorHandler', ErrorHandler);

    // Register controllers
    container.registerSingleton('HealthController', HealthController);
    container.registerSingleton('SessionController', SessionController);
  }

  static getContainer(): typeof container {
    return container;
  }

  static resolve<T>(token: string): T {
    return container.resolve<T>(token);
  }
}

// Initialize container on module load
DIContainer.initialize();
