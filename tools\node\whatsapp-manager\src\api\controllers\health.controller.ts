import { Request, Response, NextFunction } from 'express';
import { injectable, inject } from 'tsyringe';
import { IHealthService } from '../../application/services/health.service';
import { ILoggerService } from '../../shared/logging/interfaces';

@injectable()
export class HealthController {
  constructor(
    @inject('IHealthService') private healthService: IHealthService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  async getHealth(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const requestId = req.headers['x-request-id'] as string;
      const logger = requestId ? this.logger.withRequestId(requestId) : this.logger;

      logger.debug('Health check requested', {
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
      });

      const healthStatus = await this.healthService.getHealth();

      // Set appropriate HTTP status code based on health status
      let statusCode = 200;
      if (healthStatus.status === 'degraded') {
        statusCode = 200; // Still OK, but with warnings
      } else if (healthStatus.status === 'down') {
        statusCode = 503; // Service Unavailable
      }

      logger.info('Health check completed', {
        status: healthStatus.status,
        uptime: healthStatus.uptime,
        environment: healthStatus.environment,
      });

      res.status(statusCode).json(healthStatus);
    } catch (error) {
      next(error);
    }
  }
}
