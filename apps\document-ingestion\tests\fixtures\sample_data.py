"""
Sample test data and fixtures

This module provides sample data for testing the document ingestion pipeline.
"""

import json
from typing import Dict, Any, List


# Sample CSV content for testing
SAMPLE_CSV_CONTENT = b"""name,description,price,category,brand,sku
iPhone 14,Latest Apple smartphone,999.99,Electronics,Apple,APL-IP14-128
MacBook Pro,Professional laptop,2499.99,Electronics,Apple,APL-MBP-16
AirPods Pro,Wireless earbuds,249.99,Electronics,Apple,APL-APP-PRO
Coffee Mug,Ceramic coffee mug,12.99,Home,Generic,GEN-MUG-001
Notebook,Spiral notebook,3.99,Office,Moleskine,MOL-NB-A5
Pen Set,Blue ink pens,8.99,Office,Bic,BIC-PEN-10"""

# Sample CSV with missing data
SAMPLE_CSV_WITH_MISSING_DATA = b"""name,description,price,category
Product 1,Great product,29.99,Electronics
Product 2,,39.99,
Product 3,Another product,,Home
,Missing name,19.99,Books"""

# Sample CSV with special characters
SAMPLE_CSV_WITH_SPECIAL_CHARS = b"""name,description,price,category
"Product, with comma","Description with ""quotes""",29.99,Electronics
Product with newline,"Line 1\nLine 2",39.99,Home
Product with unicode,Café & Résumé,19.99,Books"""

# Sample large CSV for performance testing
def generate_large_csv_content(num_rows: int = 1000) -> bytes:
    """Generate large CSV content for testing"""
    header = "name,description,price,category,brand\n"
    rows = []
    
    for i in range(num_rows):
        row = f"Product {i},Description for product {i},{10.00 + i * 0.99},Category {i % 5},Brand {i % 3}"
        rows.append(row)
    
    content = header + "\n".join(rows)
    return content.encode('utf-8')


# Sample S3 events
SAMPLE_S3_EVENT = {
    "Records": [
        {
            "eventVersion": "2.1",
            "eventSource": "aws:s3",
            "awsRegion": "us-east-1",
            "eventTime": "2025-01-01T12:00:00.000Z",
            "eventName": "ObjectCreated:Put",
            "s3": {
                "s3SchemaVersion": "1.0",
                "configurationId": "document-ingestion-trigger",
                "bucket": {
                    "name": "test-document-bucket",
                    "ownerIdentity": {"principalId": "EXAMPLE"}
                },
                "object": {
                    "key": "user123/uploaded/products.csv",
                    "size": 1024,
                    "eTag": "example-etag",
                    "sequencer": "example-sequencer"
                }
            }
        }
    ]
}

# Sample S3 event with multiple records
SAMPLE_S3_EVENT_MULTIPLE = {
    "Records": [
        {
            "eventSource": "aws:s3",
            "s3": {
                "bucket": {"name": "test-bucket"},
                "object": {"key": "user123/uploaded/products1.csv"}
            }
        },
        {
            "eventSource": "aws:s3",
            "s3": {
                "bucket": {"name": "test-bucket"},
                "object": {"key": "user456/uploaded/products2.csv"}
            }
        }
    ]
}

# Sample API Gateway events
SAMPLE_API_GATEWAY_GET = {
    "httpMethod": "GET",
    "path": "/health",
    "headers": {
        "Content-Type": "application/json",
        "User-Agent": "test-agent"
    },
    "queryStringParameters": None,
    "body": None,
    "isBase64Encoded": False
}

SAMPLE_API_GATEWAY_POST = {
    "httpMethod": "POST",
    "path": "/process",
    "headers": {
        "Content-Type": "application/json"
    },
    "queryStringParameters": None,
    "body": json.dumps({
        "bucket": "test-bucket",
        "key": "user123/uploaded/test.csv"
    }),
    "isBase64Encoded": False
}

# Sample Lambda context
class MockLambdaContext:
    """Mock Lambda context for testing"""
    
    def __init__(self, 
                 function_name: str = "document-ingestion-test",
                 aws_request_id: str = "test-request-123",
                 remaining_time_ms: int = 300000):
        self.function_name = function_name
        self.aws_request_id = aws_request_id
        self._remaining_time_ms = remaining_time_ms
    
    def get_remaining_time_in_millis(self) -> int:
        return self._remaining_time_ms


# Sample product data
SAMPLE_PRODUCTS = [
    {
        "name": "iPhone 14",
        "description": "Latest Apple smartphone with advanced features",
        "price": "999.99",
        "category": "Electronics",
        "brand": "Apple",
        "sku": "APL-IP14-128"
    },
    {
        "name": "MacBook Pro",
        "description": "Professional laptop for developers and creators",
        "price": "2499.99",
        "category": "Electronics",
        "brand": "Apple",
        "sku": "APL-MBP-16"
    },
    {
        "name": "Coffee Mug",
        "description": "Ceramic coffee mug for daily use",
        "price": "12.99",
        "category": "Home",
        "brand": "Generic",
        "sku": "GEN-MUG-001"
    }
]

# Sample embedding vectors (simplified for testing)
SAMPLE_EMBEDDING_VECTORS = [
    [0.1, 0.2, 0.3, 0.4, 0.5] * 102 + [0.1, 0.2],  # 512 dimensions
    [0.2, 0.3, 0.4, 0.5, 0.6] * 102 + [0.2, 0.3],  # 512 dimensions
    [0.3, 0.4, 0.5, 0.6, 0.7] * 102 + [0.3, 0.4],  # 512 dimensions
]

# Sample searchable texts
SAMPLE_SEARCHABLE_TEXTS = [
    "Name: iPhone 14 | Description: Latest Apple smartphone with advanced features | Category: Electronics | Brand: Apple",
    "Name: MacBook Pro | Description: Professional laptop for developers and creators | Category: Electronics | Brand: Apple",
    "Name: Coffee Mug | Description: Ceramic coffee mug for daily use | Category: Home | Brand: Generic"
]

# Sample error scenarios
ERROR_SCENARIOS = {
    "invalid_csv": b"invalid,csv,content\nwith,missing,quotes",
    "empty_file": b"",
    "only_headers": b"name,description,price",
    "malformed_utf8": b"\xff\xfe\x00\x00invalid utf8",
    "too_large": b"x" * (100 * 1024 * 1024),  # 100MB
}

# Sample configuration for testing
TEST_CONFIG = {
    "OPENAI_API_KEY": "test-openai-key-12345",
    "OPENAI_EMBEDDING_MODEL": "text-embedding-3-small",
    "OPENAI_EMBEDDING_DIMENSIONS": "512",
    "SUPABASE_URL": "https://test-project.supabase.co",
    "SUPABASE_SERVICE_ROLE_KEY": "test-service-key-12345",
    "ENVIRONMENT": "test",
    "LOG_LEVEL": "DEBUG",
    "MAX_FILE_SIZE_BYTES": "52428800",  # 50MB
    "MAX_ROWS": "10000",
    "BATCH_SIZE": "100",
    "RETRY_ATTEMPTS": "3",
    "RETRY_MIN_WAIT": "1",
    "RETRY_MAX_WAIT": "10",
    "EMBEDDING_BATCH_SIZE": "50",
    "SUPABASE_BATCH_SIZE": "50",
    "SIMILARITY_THRESHOLD": "0.7",
    "MAX_SEARCH_RESULTS": "10"
}

# Sample Supabase responses
SAMPLE_SUPABASE_DOCUMENT_RESPONSE = {
    "data": [
        {
            "user_id": "user123",
            "document_id": "doc456",
            "s3_bucket": "test-bucket",
            "s3_key": "user123/uploaded/products.csv",
            "original_filename": "products.csv",
            "upload_status": "completed",
            "processing_stage": "completed",
            "column_names": ["name", "description", "price"],
            "total_rows": 100,
            "processed_rows": 100,
            "error_message": None,
            "created_at": "2025-01-01T12:00:00Z",
            "updated_at": "2025-01-01T12:05:00Z",
            "processing_started_at": "2025-01-01T12:01:00Z",
            "processing_completed_at": "2025-01-01T12:05:00Z"
        }
    ]
}

SAMPLE_SUPABASE_EMBEDDING_RESPONSE = {
    "data": [
        {
            "document_id": "doc456",
            "user_id": "user123",
            "row_index": 0,
            "embedding_vector": SAMPLE_EMBEDDING_VECTORS[0],
            "product_data": SAMPLE_PRODUCTS[0],
            "searchable_text": SAMPLE_SEARCHABLE_TEXTS[0],
            "created_at": "2025-01-01T12:03:00Z"
        }
    ]
}
