import { SessionEntity } from '../../src/domain/entities/SessionEntity';
import { SessionDomainService } from '../../src/domain/services/SessionDomainService';
import { ConfigService } from '../../src/shared/config/config.service';
import { LoggerService } from '../../src/shared/logging/logger.service';

describe('Session Management Basic Integration', () => {
  let configService: ConfigService;
  let loggerService: LoggerService;
  let sessionDomainService: SessionDomainService;

  beforeAll(() => {
    // Set up test environment variables
    process.env['NODE_ENV'] = 'test';
    process.env['JWT_SECRET'] = 'test-secret-key-123-minimum-32-characters-required';
    process.env['DYNAMODB_TABLE_NAME'] = 'WhatsAppSessions-test';
    process.env['AWS_REGION'] = 'ap-southeast-1';
    process.env['LOG_LEVEL'] = 'error'; // Reduce log noise in tests

    configService = new ConfigService();
    loggerService = new LoggerService(configService);
    sessionDomainService = new SessionDomainService(loggerService, configService);
  });

  describe('SessionEntity', () => {
    it('should create a valid session entity', () => {
      const session = SessionEntity.create('test-user-123');
      
      expect(session).toBeDefined();
      expect(session.userId).toBe('test-user-123');
      expect(session.status).toBe('initializing');
      expect(session.sessionId).toBeDefined();
      expect(session.createdAt).toBeInstanceOf(Date);
      expect(session.ttl).toBeDefined();
    });

    it('should serialize and deserialize correctly', () => {
      const session = SessionEntity.create('test-user-456');
      session.markAsConnected('+**********');
      
      const json = session.toJSON();
      const deserialized = SessionEntity.fromJSON(json);
      
      expect(deserialized.userId).toBe(session.userId);
      expect(deserialized.phoneNumber).toBe(session.phoneNumber);
      expect(deserialized.status).toBe(session.status);
    });
  });

  describe('SessionDomainService', () => {
    it('should validate session creation', async () => {
      await expect(sessionDomainService.validateSessionCreation('valid-user-123')).resolves.not.toThrow();
      await expect(sessionDomainService.validateSessionCreation('')).rejects.toThrow();
      await expect(sessionDomainService.validateSessionCreation('ab')).rejects.toThrow();
    });

    it('should calculate TTL correctly', () => {
      const now = new Date();
      const ttl = sessionDomainService.calculateTTL(now, 24);
      const expectedTtl = Math.floor((now.getTime() + (24 * 60 * 60 * 1000)) / 1000);
      
      expect(ttl).toBe(expectedTtl);
    });

    it('should determine if session is reusable', () => {
      const activeSession = SessionEntity.create('test-user');
      activeSession.markAsConnected('+**********');
      activeSession.updateAuthState({ creds: { test: 'creds' } as any, keys: { test: 'keys' } as any });

      expect(sessionDomainService.isSessionReusable(activeSession)).toBe(true);
      
      const expiredSession = SessionEntity.create('test-user-2');
      expiredSession.ttl = Math.floor((Date.now() - 1000) / 1000); // Expired 1 second ago
      
      expect(sessionDomainService.isSessionReusable(expiredSession)).toBe(false);
    });

    it('should calculate session health score', () => {
      const session = SessionEntity.create('test-user');
      session.markAsConnected('+**********');
      
      const health = sessionDomainService.calculateSessionHealth(session);
      
      expect(health.score).toBeGreaterThan(0);
      expect(health.score).toBeLessThanOrEqual(100);
      expect(health.factors).toBeDefined();
      expect(health.issues).toBeInstanceOf(Array);
      expect(health.recommendations).toBeInstanceOf(Array);
    });

    it('should recommend appropriate actions', () => {
      const connectedSession = SessionEntity.create('test-user');
      connectedSession.markAsConnected('+**********');
      
      const action = sessionDomainService.getRecommendedAction(connectedSession);
      expect(['maintain', 'monitor']).toContain(action);
      
      const expiredSession = SessionEntity.create('test-user-2');
      expiredSession.ttl = Math.floor((Date.now() - 1000) / 1000);
      
      const expiredAction = sessionDomainService.getRecommendedAction(expiredSession);
      expect(expiredAction).toBe('cleanup');
    });

    it('should validate status transitions', () => {
      expect(sessionDomainService.validateStatusTransition('initializing', 'qr_pending')).toBe(true);
      expect(sessionDomainService.validateStatusTransition('qr_pending', 'connected')).toBe(true);
      expect(sessionDomainService.validateStatusTransition('connected', 'initializing')).toBe(false);
    });

    it('should calculate reconnection delay', () => {
      const delay1 = sessionDomainService.calculateReconnectionDelay(0);
      const delay2 = sessionDomainService.calculateReconnectionDelay(3);
      
      expect(delay1).toBeGreaterThan(0);
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay2).toBeLessThanOrEqual(5 * 60 * 1000); // Max 5 minutes
    });
  });

  describe('Configuration Integration', () => {
    it('should load session-related configuration', () => {
      const dbConfig = configService.getDatabase();
      expect(dbConfig.tableName).toBe('WhatsAppSessions-test');
      expect(dbConfig.region).toBe('ap-southeast-1');
      
      const authConfig = configService.getAuth();
      expect(authConfig.jwtSecret).toBeDefined();
      expect(authConfig.jwtSecret.length).toBeGreaterThanOrEqual(32);
    });

    it('should provide default values for optional config', () => {
      const maxSessions = configService.getOptional('MAX_CONCURRENT_SESSIONS', '100');
      expect(parseInt(maxSessions)).toBe(100);
      
      const ttlHours = configService.getOptional('SESSION_TTL_HOURS', '72');
      expect(parseInt(ttlHours)).toBe(72);
    });
  });

  describe('Logging Integration', () => {
    it('should create child loggers', () => {
      const childLogger = loggerService.child({ component: 'test' });
      expect(childLogger).toBeDefined();
      
      // Test that logging doesn't throw errors
      expect(() => {
        childLogger.info('Test log message');
        childLogger.debug('Debug message');
        childLogger.warn('Warning message');
      }).not.toThrow();
    });
  });
});
