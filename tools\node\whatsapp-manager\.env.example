# Environment Configuration Template
# Copy this file to environments/.env.development.local and customize for your environment

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Runtime environment (development, uat, production)
NODE_ENV=development

# Server configuration
PORT=3000
HOST=0.0.0.0

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

# AWS region for all services
AWS_REGION=ap-southeast-1

# DynamoDB configuration
# For development: use local DynamoDB endpoint
# For UAT/Production: leave DYNAMODB_ENDPOINT unset to use AWS DynamoDB
DYNAMODB_ENDPOINT=http://dynamodb-local:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev

# AWS Secrets Manager (UAT/Production only)
# MY_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-1:123456789012:secret:whatsapp-api-secrets

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT configuration
JWT_SECRET=dev-super-secret-key-123-change-in-production
QR_TOKEN_EXPIRY_SEC=300

# CORS configuration
CORS_ORIGINS=*

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: debug, info, warn, error
LOG_LEVEL=debug

# Log format: pretty (development), json (production)
LOG_FORMAT=pretty

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
