name: Reusable - Docker Build and Push

on:
  workflow_call:
    inputs:
      lambda-path:
        required: true
        type: string
        description: 'Path to Lambda function directory'
      function-name:
        required: true
        type: string
        description: 'Function name for ECR repository'
      aws-region:
        required: true
        type: string
        description: 'AWS region'
      ecr-account-id:
        required: true
        type: string
        description: 'ECR account ID'
      uat-account-id:
        required: true
        type: string
        description: 'UAT account ID for role assumption'
      github-oidc-role:
        required: true
        type: string
        description: 'GitHub OIDC role name'
      target-role:
        required: true
        type: string
        description: 'Target role name for cross-account assumption'
    outputs:
      image-uri:
        description: 'Built Docker image URI'
        value: ${{ jobs.build-and-push.outputs.image-uri }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    outputs:
      image-uri: ${{ steps.build-image.outputs.image-uri }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS OIDC role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ inputs.ecr-account-id }}:role/${{ inputs.github-oidc-role }}
          role-session-name: GitHubActions-OIDC-${{ github.run_id }}
          aws-region: ${{ inputs.aws-region }}

      - name: Assume UAT role for ECR operations
        run: |
          echo "🔍 Current identity before UAT role assumption:"
          aws sts get-caller-identity

          echo "🔄 Assuming UAT role for ECR operations..."
          OUTPUT=$(aws sts assume-role --role-arn arn:aws:iam::${{ inputs.uat-account-id }}:role/${{ inputs.target-role }} --role-session-name GitHubActions-ECR-${{ github.run_id }})

          # Set new credentials
          echo "AWS_ACCESS_KEY_ID=$(echo $OUTPUT | jq -r .Credentials.AccessKeyId)" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $OUTPUT | jq -r .Credentials.SecretAccessKey)" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $OUTPUT | jq -r .Credentials.SessionToken)" >> $GITHUB_ENV

          echo "✅ Verifying new UAT identity:"
          aws sts get-caller-identity

      - name: Create ECR repository if not exists
        run: |
          REPO_NAME="${{ inputs.function-name }}"
          
          if ! aws ecr describe-repositories --repository-names $REPO_NAME --region ${{ inputs.aws-region }} >/dev/null 2>&1; then
            echo "🆕 Creating ECR repository: $REPO_NAME"
            aws ecr create-repository \
              --repository-name $REPO_NAME \
              --region ${{ inputs.aws-region }}
          else
            echo "✅ ECR repository already exists: $REPO_NAME"
          fi

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ${{ inputs.aws-region }} | \
          docker login --username AWS --password-stdin ${{ inputs.ecr-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com

      - name: Build and push Docker image
        id: build-image
        run: |
          REPO_URI="${{ inputs.ecr-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com/${{ inputs.function-name }}"
          IMAGE_TAG="${{ github.run_number }}"
          IMAGE_URI="$REPO_URI:$IMAGE_TAG"
          
          echo "🏗️ Building Docker image..."
          cd ${{ inputs.lambda-path }}
          docker build -t ${{ inputs.function-name }}:$IMAGE_TAG .
          docker tag ${{ inputs.function-name }}:$IMAGE_TAG $IMAGE_URI
          
          echo "📤 Pushing Docker image to ECR..."
          docker push $IMAGE_URI
          
          echo "✅ Image pushed successfully: $IMAGE_URI"
          echo "image-uri=$IMAGE_URI" >> $GITHUB_OUTPUT
