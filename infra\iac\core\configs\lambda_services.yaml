# Lambda Services Configuration
# This file defines all Lambda services deployed across environments.
# Environment-specific settings are handled via Terraform variables.

sample-lambda:
  # Container configuration
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-nodejs-sample:latest

  # Environment variables (AWS_REGION is automatically set by Lambda)
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"

  # API Gateway path patterns (like ECS) - simple and intuitive
  api_gateway:
    path_patterns:
      - "/sample"    # Exact match for /sample
      - "/sample/*"  # Matches /sample/* (any HTTP method)

  # Tags
  tags:
    Service: "Sample Lambda"
    Team: "Platform"
    ECRAccount: "************"