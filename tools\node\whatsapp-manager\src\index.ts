import 'reflect-metadata';
import './di/container'; // Initialize DI container
import { App } from './app';
import { DIContainer } from './di/container';
import { IConfigService } from './shared/config/interfaces';
import { ILoggerService } from './shared/logging/interfaces';

/**
 * Application bootstrap and startup
 */
async function bootstrap(): Promise<void> {
  let logger: ILoggerService | undefined;

  try {
    // Resolve services from DI container
    const configService = DIContainer.resolve<IConfigService>('IConfigService');
    logger = DIContainer.resolve<ILoggerService>('ILoggerService');

    // Validate configuration
    await configService.validate();
    logger.info('Configuration validation passed');

    // Create and start application
    const app = new App();
    await app.start();

  } catch (error) {
    // If logger is not available, fall back to console
    if (logger) {
      logger.error('Application failed to start', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    } else {
      console.error('Application failed to start:', error);
    }

    process.exit(1);
  }
}

// Start the application
bootstrap().catch((error) => {
  console.error('Bootstrap failed:', error);
  process.exit(1);
});
