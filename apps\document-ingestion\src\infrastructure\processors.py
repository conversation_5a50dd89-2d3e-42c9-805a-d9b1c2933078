"""
CSV Processing Implementations for Document Ingestion Pipeline

This module provides concrete implementations of CSV processing interfaces
using pandas for data manipulation and validation.
"""

import io
from typing import List, Tuple

import pandas as pd

from ..domain.entities import ProductData
from ..domain.interfaces import ICSVProcessor, ILogger, IConfiguration


class PandasCSVProcessor(ICSVProcessor):
    """Pandas implementation of CSV processor"""
    
    def __init__(self, config: IConfiguration, logger: ILogger):
        self._config = config
        self._logger = logger
    
    async def parse_csv(self, content: bytes) -> Tuple[List[str], List[ProductData]]:
        """Parse CSV content and return column names and product data"""
        try:
            # Parse CSV with pandas
            df = pd.read_csv(io.BytesIO(content))
            
            self._logger.info(
                f"Parsed CSV with {len(df)} rows and {len(df.columns)} columns"
            )
            
            # Get column names
            column_names = df.columns.tolist()
            
            # Convert to ProductData objects
            products = []
            for index, row in df.iterrows():
                product_data = ProductData(
                    data=row.to_dict(),
                    row_index=int(index)
                )
                products.append(product_data)
            
            return column_names, products
            
        except Exception as e:
            self._logger.error(f"Failed to parse CSV: {e}", error=e)
            raise ValueError(f"Failed to parse CSV: {e}")
    
    def validate_csv_data(self, data: List[ProductData]) -> bool:
        """Validate CSV data"""
        try:
            if not data:
                raise ValueError("CSV data is empty")
            
            max_rows = self._config.get_max_rows()
            if len(data) > max_rows:
                raise ValueError(f"CSV file too large: {len(data)} rows (max: {max_rows})")
            
            # Check for basic data integrity
            for i, product in enumerate(data):
                if not isinstance(product.data, dict):
                    raise ValueError(f"Invalid product data at row {i}: not a dictionary")
                
                if product.row_index != i:
                    raise ValueError(f"Row index mismatch at row {i}: expected {i}, got {product.row_index}")
            
            self._logger.info(f"CSV validation passed for {len(data)} rows")
            return True
            
        except Exception as e:
            self._logger.error(f"CSV validation failed: {e}", error=e)
            raise ValueError(f"CSV validation failed: {e}")
    
    def clean_csv_data(self, data: List[ProductData]) -> List[ProductData]:
        """Clean CSV data"""
        try:
            self._logger.info(f"Cleaning CSV data: {len(data)} rows")
            
            cleaned_data = []
            
            for product in data:
                # Clean the product data dictionary
                cleaned_dict = {}
                
                for key, value in product.data.items():
                    # Clean key
                    clean_key = str(key).strip() if key is not None else ""
                    if not clean_key:
                        continue
                    
                    # Clean value
                    if value is None or value == "" or str(value).strip() == "":
                        cleaned_dict[clean_key] = None
                    elif isinstance(value, str):
                        # Clean string values
                        clean_value = value.strip()
                        # Replace null-like strings with None
                        if clean_value.lower() in ["null", "none", "n/a", "#n/a", "nan"]:
                            cleaned_dict[clean_key] = None
                        else:
                            cleaned_dict[clean_key] = clean_value
                    else:
                        cleaned_dict[clean_key] = value
                
                # Only keep products with at least some non-null data
                if any(v is not None for v in cleaned_dict.values()):
                    cleaned_product = ProductData(
                        data=cleaned_dict,
                        row_index=product.row_index
                    )
                    cleaned_data.append(cleaned_product)
            
            self._logger.info(
                f"Data cleaning completed: {len(cleaned_data)} rows "
                f"(removed {len(data) - len(cleaned_data)} empty rows)"
            )
            
            return cleaned_data
            
        except Exception as e:
            self._logger.error(f"Error cleaning CSV data: {e}", error=e)
            raise ValueError(f"Failed to clean CSV data: {e}")


class EnhancedCSVProcessor(PandasCSVProcessor):
    """Enhanced CSV processor with additional validation and cleaning features"""
    
    def validate_csv_data(self, data: List[ProductData]) -> bool:
        """Enhanced validation with data quality checks"""
        # Run basic validation first
        super().validate_csv_data(data)
        
        try:
            # Calculate data quality metrics
            total_fields = 0
            non_empty_fields = 0
            
            for product in data:
                for value in product.data.values():
                    total_fields += 1
                    if value is not None and str(value).strip():
                        non_empty_fields += 1
            
            data_quality_score = non_empty_fields / total_fields if total_fields > 0 else 0.0
            
            self._logger.info(f"Data quality score: {data_quality_score:.2%}")
            
            # Warn if data quality is low
            if data_quality_score < 0.3:
                self._logger.warning(
                    f"Low data quality score: {data_quality_score:.2%} - "
                    f"consider improving data before processing"
                )
            
            # Check for duplicate rows
            seen_data = set()
            duplicates = 0
            
            for product in data:
                # Create a hashable representation of the data
                data_tuple = tuple(sorted(product.data.items()))
                if data_tuple in seen_data:
                    duplicates += 1
                else:
                    seen_data.add(data_tuple)
            
            if duplicates > 0:
                self._logger.warning(f"Found {duplicates} duplicate rows")
            
            return True
            
        except Exception as e:
            self._logger.error(f"Enhanced CSV validation failed: {e}", error=e)
            raise ValueError(f"Enhanced CSV validation failed: {e}")
    
    def clean_csv_data(self, data: List[ProductData]) -> List[ProductData]:
        """Enhanced cleaning with deduplication"""
        # Run basic cleaning first
        cleaned_data = super().clean_csv_data(data)
        
        try:
            # Remove duplicates while preserving order
            seen_data = set()
            deduplicated_data = []
            
            for product in cleaned_data:
                # Create a hashable representation of the data
                data_tuple = tuple(sorted(product.data.items()))
                if data_tuple not in seen_data:
                    seen_data.add(data_tuple)
                    deduplicated_data.append(product)
            
            removed_duplicates = len(cleaned_data) - len(deduplicated_data)
            if removed_duplicates > 0:
                self._logger.info(f"Removed {removed_duplicates} duplicate rows")
            
            return deduplicated_data
            
        except Exception as e:
            self._logger.error(f"Error in enhanced CSV cleaning: {e}", error=e)
            raise ValueError(f"Failed to perform enhanced CSV cleaning: {e}")
