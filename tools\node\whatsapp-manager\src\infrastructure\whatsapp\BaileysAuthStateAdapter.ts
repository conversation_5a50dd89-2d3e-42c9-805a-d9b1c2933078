import { inject, injectable } from 'tsyringe';
// <PERSON><PERSON> types for development
type SignalDataTypeMap = any;

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { IAuthStateAdapter } from '../../domain/services/IWhatsAppService';
import { AuthenticationState } from '../../domain/entities/SessionEntity';
import { ILoggerService } from '../../shared/logging/interfaces';

/**
 * Baileys authentication state adapter
 * Implements the Baileys auth state interface using our session repository
 */
@injectable()
export class BaileysAuthStateAdapter implements IAuthStateAdapter {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Load authentication state from storage
   */
  async loadAuthState(userId: string): Promise<AuthenticationState | null> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session || !session.authState) {
        return null;
      }

      return session.authState;
    } catch (error) {
      this.logger.error('Failed to load auth state', {
        userId,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Save authentication state to storage
   */
  async saveAuthState(userId: string, authState: AuthenticationState): Promise<void> {
    try {
      await this.sessionRepository.updateAuthState(userId, authState);
      this.logger.debug('Auth state saved', { userId });
    } catch (error) {
      this.logger.error('Failed to save auth state', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState(userId: string): Promise<void> {
    try {
      await this.sessionRepository.updateAuthState(userId, null as any);
      this.logger.debug('Auth state cleared', { userId });
    } catch (error) {
      this.logger.error('Failed to clear auth state', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Check if auth state exists
   */
  async hasAuthState(userId: string): Promise<boolean> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      return !!(session && session.authState);
    } catch (error) {
      this.logger.error('Failed to check auth state existence', {
        userId,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Create Baileys-compatible auth state object
   */
  async createBaileysAuthState(userId: string): Promise<{
    state: AuthenticationState;
    saveCreds: () => Promise<void>;
  }> {
    // Load existing auth state or create new one
    let authState = await this.loadAuthState(userId);
    
    if (!authState) {
      // Initialize mock auth credentials for development
      const creds = { mockCreds: true };
      authState = {
        creds,
        keys: this.createEmptyKeyStore()
      };
    }

    // Create save function for Baileys
    const saveCreds = async () => {
      await this.saveAuthState(userId, authState!);
    };

    return {
      state: authState,
      saveCreds
    };
  }

  /**
   * Create Baileys-compatible key store
   */
  private createEmptyKeyStore(): any {
    const keys: any = {};

    return {
      get: async (type: keyof SignalDataTypeMap, ids: string[]) => {
        const data: { [id: string]: any } = {};
        for (const id of ids) {
          const key = `${String(type)}:${String(id)}`;
          if (keys[key]) {
            data[id] = keys[key];
          }
        }
        return data;
      },

      set: async (data: any) => {
        for (const category in data) {
          if (Object.prototype.hasOwnProperty.call(data, category)) {
            for (const id in data[category]) {
              if (Object.prototype.hasOwnProperty.call(data[category], id)) {
                const key = `${String(category)}:${String(id)}`;
                keys[key] = data[category][id];
              }
            }
          }
        }
      }
    };
  }

  /**
   * Serialize auth state for storage
   */
  static serializeAuthState(authState: AuthenticationState): string {
    return JSON.stringify(authState);
  }

  /**
   * Deserialize auth state from storage
   */
  static deserializeAuthState(serialized: string): AuthenticationState {
    return JSON.parse(serialized);
  }

  /**
   * Validate auth state integrity
   */
  validateAuthState(authState: AuthenticationState): boolean {
    try {
      if (!authState || typeof authState !== 'object') {
        return false;
      }

      if (!authState.creds || typeof authState.creds !== 'object') {
        return false;
      }

      if (!authState.keys || typeof authState.keys !== 'object') {
        return false;
      }

      // Basic validation for mock auth state
      if (!authState.creds || typeof authState.creds !== 'object') {
        this.logger.warn('Invalid credentials in auth state');
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Auth state validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Create a backup of auth state
   */
  async backupAuthState(userId: string): Promise<string | null> {
    try {
      const authState = await this.loadAuthState(userId);
      if (!authState) {
        return null;
      }

      return BaileysAuthStateAdapter.serializeAuthState(authState);
    } catch (error) {
      this.logger.error('Failed to backup auth state', {
        userId,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Restore auth state from backup
   */
  async restoreAuthState(userId: string, backup: string): Promise<boolean> {
    try {
      const authState = BaileysAuthStateAdapter.deserializeAuthState(backup);
      
      if (!this.validateAuthState(authState)) {
        this.logger.error('Invalid auth state in backup', { userId });
        return false;
      }

      await this.saveAuthState(userId, authState);
      this.logger.info('Auth state restored from backup', { userId });
      return true;
    } catch (error) {
      this.logger.error('Failed to restore auth state from backup', {
        userId,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get auth state metadata
   */
  async getAuthStateMetadata(userId: string): Promise<{
    exists: boolean;
    createdAt?: Date;
    lastUpdated?: Date;
    phoneNumber?: string;
    deviceId?: string;
  }> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      
      if (!session || !session.authState) {
        return { exists: false };
      }

      return {
        exists: true,
        createdAt: session.createdAt,
        lastUpdated: session.updatedAt,
        phoneNumber: session.phoneNumber || undefined,
        deviceId: session.authState.creds?.registrationId?.toString()
      };
    } catch (error) {
      this.logger.error('Failed to get auth state metadata', {
        userId,
        error: (error as Error).message
      });
      return { exists: false };
    }
  }

  /**
   * Clean up expired auth states
   */
  async cleanupExpiredAuthStates(): Promise<number> {
    try {
      const expiredSessions = await this.sessionRepository.findExpiredSessions();
      let cleanedCount = 0;

      for (const session of expiredSessions) {
        try {
          await this.clearAuthState(session.userId);
          cleanedCount++;
        } catch (error) {
          this.logger.warn('Failed to cleanup auth state for expired session', {
            userId: session.userId,
            error: (error as Error).message
          });
        }
      }

      this.logger.info('Auth state cleanup completed', {
        expiredSessions: expiredSessions.length,
        cleanedCount
      });

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired auth states', {
        error: (error as Error).message
      });
      return 0;
    }
  }
}
