"""
AWS Secrets Manager Integration for Document Ingestion Pipeline

This module provides secure access to secrets stored in AWS Secrets Manager,
with fallback to environment variables for local development.
"""

import os
import json
import boto3
from typing import Optional, Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError


class SecretsManager:
    """AWS Secrets Manager client with fallback to environment variables"""
    
    def __init__(self):
        self._client = None
        self._cache: Dict[str, Any] = {}
        self._is_aws_environment = self._detect_aws_environment()
    
    def _detect_aws_environment(self) -> bool:
        """Detect if running in AWS environment (Lambda, EC2, etc.)"""
        # Check for AWS Lambda environment
        if os.getenv('AWS_LAMBDA_FUNCTION_NAME'):
            return True
        
        # Check for AWS execution environment
        if os.getenv('AWS_EXECUTION_ENV'):
            return True
        
        # Check for explicit AWS credentials
        if os.getenv('AWS_ACCESS_KEY_ID') and os.getenv('AWS_SECRET_ACCESS_KEY'):
            return True
        
        # Check for LocalStack
        if os.getenv('AWS_ENDPOINT_URL'):
            return True
        
        return False
    
    @property
    def client(self):
        """Lazy initialization of AWS Secrets Manager client"""
        if self._client is None:
            try:
                # Configure client for LocalStack or AWS
                endpoint_url = os.getenv('AWS_ENDPOINT_URL')
                region = os.getenv('AWS_REGION', os.getenv('AWS_DEFAULT_REGION', 'us-east-1'))
                
                if endpoint_url:
                    # LocalStack configuration
                    self._client = boto3.client(
                        'secretsmanager',
                        endpoint_url=endpoint_url,
                        region_name=region,
                        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID', 'test'),
                        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY', 'test')
                    )
                else:
                    # AWS configuration (uses IAM roles in Lambda)
                    self._client = boto3.client('secretsmanager', region_name=region)
                    
            except (NoCredentialsError, ClientError) as e:
                print(f"Warning: Could not initialize AWS Secrets Manager client: {e}")
                self._client = None
        
        return self._client
    
    def get_secret(self, secret_name: str, fallback_env_var: Optional[str] = None) -> Optional[str]:
        """
        Get secret value from AWS Secrets Manager with fallback to environment variable
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Secret value or None if not found
        """
        # Check cache first
        if secret_name in self._cache:
            return self._cache[secret_name]
        
        # Try AWS Secrets Manager if in AWS environment
        if self._is_aws_environment and self.client:
            try:
                response = self.client.get_secret_value(SecretId=secret_name)
                secret_value = response['SecretString']
                
                # Try to parse as JSON (for complex secrets)
                try:
                    secret_data = json.loads(secret_value)
                    # If it's a JSON object, cache the whole object
                    self._cache[secret_name] = secret_data
                    return secret_data
                except json.JSONDecodeError:
                    # It's a plain string secret
                    self._cache[secret_name] = secret_value
                    return secret_value
                    
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == 'ResourceNotFoundException':
                    print(f"Secret {secret_name} not found in AWS Secrets Manager")
                else:
                    print(f"Error retrieving secret {secret_name}: {e}")
        
        # Fallback to environment variable
        if fallback_env_var:
            env_value = os.getenv(fallback_env_var)
            if env_value:
                self._cache[secret_name] = env_value
                return env_value
        
        return None
    
    def get_secret_string(self, secret_name: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get secret as string with empty string fallback
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Secret value or empty string if not found
        """
        value = self.get_secret(secret_name, fallback_env_var)
        return value if isinstance(value, str) else ''
    
    def get_secret_json(self, secret_name: str, key: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get specific key from JSON secret
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            key: Key within the JSON secret
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Secret value or empty string if not found
        """
        secret_data = self.get_secret(secret_name, fallback_env_var)
        
        if isinstance(secret_data, dict):
            return secret_data.get(key, '')
        
        return ''
    
    def clear_cache(self):
        """Clear the secrets cache"""
        self._cache.clear()


# Global instance for use across the application
secrets_manager = SecretsManager()


def get_openai_api_key() -> str:
    """Get OpenAI API key from secrets or environment"""
    return secrets_manager.get_secret_string(
        'lambda/openai-api-key',
        'OPENAI_API_KEY'
    )


def get_supabase_url() -> str:
    """Get Supabase URL from secrets or environment"""
    return secrets_manager.get_secret_string(
        'lambda/supabase-url',
        'SUPABASE_URL'
    )


def get_supabase_service_role_key() -> str:
    """Get Supabase service role key from secrets or environment"""
    return secrets_manager.get_secret_string(
        'lambda/supabase-service-role-key',
        'SUPABASE_SERVICE_ROLE_KEY'
    )


def get_supabase_anon_key() -> str:
    """Get Supabase anonymous key from secrets or environment"""
    return secrets_manager.get_secret_string(
        'lambda/supabase-anon-key',
        'SUPABASE_ANON_KEY'
    )


def get_all_secrets() -> Dict[str, str]:
    """Get all secrets for debugging (without sensitive values)"""
    return {
        'has_openai_key': bool(get_openai_api_key()),
        'has_supabase_url': bool(get_supabase_url()),
        'has_supabase_service_key': bool(get_supabase_service_role_key()),
        'has_supabase_anon_key': bool(get_supabase_anon_key()),
        'is_aws_environment': secrets_manager._is_aws_environment,
        'secrets_cached': len(secrets_manager._cache)
    }
