import { Router } from 'express';
import { createHealthRoutes } from './health.routes';
import { createSessionRoutes } from './session.routes';

/**
 * Main API routes configuration
 */
export function createApiRoutes(): Router {
  const router = Router();

  // Health check routes
  router.use('/health', createHealthRoutes());

  // Session management routes
  router.use('/sessions', createSessionRoutes());

  // Future routes will be added here:
  // router.use('/auth', createAuthRoutes());
  // router.use('/messages', createMessageRoutes());

  return router;
}
