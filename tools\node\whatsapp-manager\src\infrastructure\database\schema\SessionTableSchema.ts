import { SessionStatus } from '../../../domain/entities/SessionEntity';

/**
 * DynamoDB item structure for session storage
 */
export interface SessionDynamoDBItem {
  PK: string;           // userId
  SK: string;           // session#{userId}
  GSI1PK: string;       // status#{status}
  GSI1SK: string;       // createdAt (for sorting)
  userId: string;
  sessionId: string;
  phoneNumber?: string;
  authState?: string;   // JSON serialized AuthenticationState
  status: SessionStatus;
  createdAt: string;    // ISO string
  updatedAt: string;    // ISO string
  connectedAt?: string; // ISO string
  disconnectedAt?: string; // ISO string
  ttl?: number;         // Unix timestamp for automatic cleanup
  deviceName?: string;
  browserName?: string;
}

/**
 * DynamoDB table definition for session storage
 */
export const SESSION_TABLE_DEFINITION = {
  TableName: '${DYNAMODB_TABLE_NAME}', // Will be replaced by actual table name
  KeySchema: [
    { AttributeName: 'PK', KeyType: 'HASH' },
    { AttributeName: 'SK', KeyType: 'RANGE' }
  ],
  AttributeDefinitions: [
    { AttributeName: 'PK', AttributeType: 'S' },
    { AttributeName: 'SK', AttributeType: 'S' },
    { AttributeName: 'GSI1PK', AttributeType: 'S' },
    { AttributeName: 'GSI1SK', AttributeType: 'S' }
  ],
  GlobalSecondaryIndexes: [{
    IndexName: 'StatusIndex',
    KeySchema: [
      { AttributeName: 'GSI1PK', KeyType: 'HASH' },
      { AttributeName: 'GSI1SK', KeyType: 'RANGE' }
    ],
    Projection: { ProjectionType: 'ALL' },
    BillingMode: 'PAY_PER_REQUEST'
  }],
  BillingMode: 'PAY_PER_REQUEST',
  TimeToLiveSpecification: {
    AttributeName: 'ttl',
    Enabled: true
  },
  StreamSpecification: {
    StreamEnabled: true,
    StreamViewType: 'NEW_AND_OLD_IMAGES'
  }
} as const;

/**
 * DynamoDB key patterns for session operations
 */
export const SESSION_KEY_PATTERNS = {
  /**
   * Primary key for session items
   */
  sessionPK: (userId: string) => userId,
  sessionSK: (userId: string) => `session#${userId}`,

  /**
   * GSI1 key for status-based queries
   */
  statusGSI1PK: (status: SessionStatus) => `status#${status}`,
  statusGSI1SK: (createdAt: Date) => createdAt.toISOString(),

  /**
   * Parse keys back to components
   */
  parseSessionSK: (sk: string) => {
    const match = sk.match(/^session#(.+)$/);
    return match ? match[1] : null;
  },

  parseStatusGSI1PK: (gsi1pk: string) => {
    const match = gsi1pk.match(/^status#(.+)$/);
    return match ? match[1] as SessionStatus : null;
  }
} as const;

/**
 * DynamoDB condition expressions for session operations
 */
export const SESSION_CONDITIONS = {
  /**
   * Condition to ensure item doesn't exist (for create operations)
   */
  ITEM_NOT_EXISTS: 'attribute_not_exists(PK)',

  /**
   * Condition to ensure item exists (for update operations)
   */
  ITEM_EXISTS: 'attribute_exists(PK)',

  /**
   * Condition to check specific status
   */
  STATUS_EQUALS: (_status: SessionStatus) => `#status = :status`,

  /**
   * Condition to check TTL not expired
   */
  TTL_NOT_EXPIRED: 'attribute_not_exists(#ttl) OR #ttl > :currentTime',

  /**
   * Condition to check session is active
   */
  SESSION_ACTIVE: `#status = :connectedStatus AND (attribute_not_exists(#ttl) OR #ttl > :currentTime)`
} as const;

/**
 * DynamoDB update expressions for common session operations
 */
export const SESSION_UPDATE_EXPRESSIONS = {
  /**
   * Update session status
   */
  UPDATE_STATUS: {
    UpdateExpression: 'SET #status = :status, #updatedAt = :updatedAt, #gsi1pk = :gsi1pk',
    ExpressionAttributeNames: {
      '#status': 'status',
      '#updatedAt': 'updatedAt',
      '#gsi1pk': 'GSI1PK'
    }
  },

  /**
   * Update auth state
   */
  UPDATE_AUTH_STATE: {
    UpdateExpression: 'SET #authState = :authState, #updatedAt = :updatedAt',
    ExpressionAttributeNames: {
      '#authState': 'authState',
      '#updatedAt': 'updatedAt'
    }
  },

  /**
   * Update connection info
   */
  UPDATE_CONNECTION: {
    UpdateExpression: 'SET #phoneNumber = :phoneNumber, #connectedAt = :connectedAt, #status = :status, #updatedAt = :updatedAt, #gsi1pk = :gsi1pk',
    ExpressionAttributeNames: {
      '#phoneNumber': 'phoneNumber',
      '#connectedAt': 'connectedAt',
      '#status': 'status',
      '#updatedAt': 'updatedAt',
      '#gsi1pk': 'GSI1PK'
    }
  },

  /**
   * Mark as disconnected
   */
  UPDATE_DISCONNECTION: {
    UpdateExpression: 'SET #disconnectedAt = :disconnectedAt, #status = :status, #updatedAt = :updatedAt, #gsi1pk = :gsi1pk',
    ExpressionAttributeNames: {
      '#disconnectedAt': 'disconnectedAt',
      '#status': 'status',
      '#updatedAt': 'updatedAt',
      '#gsi1pk': 'GSI1PK'
    }
  }
} as const;

/**
 * DynamoDB query patterns for session operations
 */
export const SESSION_QUERIES = {
  /**
   * Find sessions by status
   */
  BY_STATUS: {
    IndexName: 'StatusIndex',
    KeyConditionExpression: 'GSI1PK = :statusPK',
    ScanIndexForward: false // Most recent first
  },

  /**
   * Find active sessions
   */
  ACTIVE_SESSIONS: {
    IndexName: 'StatusIndex',
    KeyConditionExpression: 'GSI1PK = :statusPK',
    FilterExpression: 'attribute_not_exists(#ttl) OR #ttl > :currentTime',
    ExpressionAttributeNames: {
      '#ttl': 'ttl'
    },
    ScanIndexForward: false
  },

  /**
   * Find expired sessions
   */
  EXPIRED_SESSIONS: {
    IndexName: 'StatusIndex',
    FilterExpression: 'attribute_exists(#ttl) AND #ttl <= :currentTime',
    ExpressionAttributeNames: {
      '#ttl': 'ttl'
    }
  }
} as const;

/**
 * Helper functions for DynamoDB operations
 */
export const SESSION_HELPERS = {
  /**
   * Get current Unix timestamp
   */
  getCurrentUnixTimestamp: () => Math.floor(Date.now() / 1000),

  /**
   * Calculate TTL timestamp
   */
  calculateTTL: (hours: number = 72) => Math.floor((Date.now() + (hours * 60 * 60 * 1000)) / 1000),

  /**
   * Check if TTL is expired
   */
  isTTLExpired: (ttl?: number) => {
    if (!ttl) return false;
    return SESSION_HELPERS.getCurrentUnixTimestamp() > ttl;
  },

  /**
   * Sanitize auth state for storage
   */
  sanitizeAuthState: (authState: any) => {
    if (!authState) return undefined;
    try {
      return JSON.stringify(authState);
    } catch (error) {
      throw new Error('Invalid auth state: cannot serialize to JSON');
    }
  },

  /**
   * Parse auth state from storage
   */
  parseAuthState: (authStateString?: string) => {
    if (!authStateString) return null;
    try {
      return JSON.parse(authStateString);
    } catch (error) {
      throw new Error('Invalid auth state: cannot parse JSON');
    }
  }
} as const;
