[flake8]
max-line-length = 100
max-complexity = 10
select = E,W,F,C
ignore =
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    F401,  # imported but unused (in __init__.py files)
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov,
    tests/fixtures,
per-file-ignores =
    __init__.py:F401
    tests/*:F401,F811
    conftest.py:F401,F811
count = True
statistics = True
show-source = True