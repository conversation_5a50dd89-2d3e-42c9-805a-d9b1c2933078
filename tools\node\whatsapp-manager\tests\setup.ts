import 'reflect-metadata';

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env['NODE_ENV'] = 'test';
  process.env['JWT_SECRET'] = 'test-secret-key-123-minimum-32-characters-required';
  process.env['DYNAMODB_TABLE_NAME'] = 'WhatsAppSessions-test';
  process.env['AWS_REGION'] = 'ap-southeast-1';
  process.env['LOG_LEVEL'] = 'error'; // Reduce log noise in tests
  process.env['LOG_FORMAT'] = 'json';
});

afterAll(async () => {
  // Cleanup after all tests
});

// Increase timeout for integration tests
jest.setTimeout(10000);
