"""
Unit tests for dependency injection container

These tests verify that the dependency injection container
properly wires dependencies and manages their lifecycle.
"""

import pytest
import os
from unittest.mock import Mock, patch

from src.infrastructure.dependency_injection import ApplicationContainer
from src.infrastructure.configuration import EnvironmentConfiguration
from src.infrastructure.logging import DomainLogger
from src.domain.services import SearchableTextGenerator


class TestApplicationContainer:
    """Test ApplicationContainer dependency injection"""
    
    @pytest.fixture
    def container(self):
        """Create a fresh container for each test"""
        return ApplicationContainer()
    
    def test_container_creation(self, container):
        """Test that container can be created"""
        assert container is not None
        # Check that it's a container instance (dependency-injector creates DynamicContainer instances)
        assert hasattr(container, 'config')
        assert hasattr(container, 'logger')
        assert hasattr(container, 'process_document_use_case')
    
    def test_config_provider(self, container):
        """Test configuration provider"""
        config = container.config()
        
        assert config is not None
        assert isinstance(config, EnvironmentConfiguration)
    
    def test_logger_provider(self, container):
        """Test logger provider"""
        logger = container.logger()
        
        assert logger is not None
        assert isinstance(logger, DomainLogger)
    
    def test_metrics_collector_provider(self, container):
        """Test metrics collector provider"""
        metrics = container.metrics_collector()
        
        assert metrics is not None
        # Should be CloudWatchMetricsCollector or SimpleMetricsCollector
        assert hasattr(metrics, 'record_processing_time')
        assert hasattr(metrics, 'record_error')
        assert hasattr(metrics, 'record_success')
    
    def test_searchable_text_generator_provider(self, container):
        """Test searchable text generator provider"""
        generator = container.searchable_text_generator()
        
        assert generator is not None
        assert isinstance(generator, SearchableTextGenerator)
    
    def test_domain_services_providers(self, container):
        """Test domain services providers"""
        validation_service = container.document_validation_service()
        security_service = container.security_service()
        embedding_gen_service = container.embedding_generation_service()
        
        assert validation_service is not None
        assert security_service is not None
        assert embedding_gen_service is not None
        
        # Check they have expected methods
        assert hasattr(validation_service, 'validate_file_size')
        assert hasattr(security_service, 'validate_tenant_access')
        assert hasattr(embedding_gen_service, 'generate_product_embeddings')
    
    def test_infrastructure_services_providers(self, container):
        """Test infrastructure services providers"""
        document_repo = container.document_repository()
        embedding_repo = container.embedding_repository()
        file_storage = container.file_storage()
        embedding_service = container.embedding_service()
        csv_processor = container.csv_processor()
        event_publisher = container.event_publisher()
        
        assert document_repo is not None
        assert embedding_repo is not None
        assert file_storage is not None
        assert embedding_service is not None
        assert csv_processor is not None
        assert event_publisher is not None
        
        # Check they have expected methods
        assert hasattr(document_repo, 'save')
        assert hasattr(embedding_repo, 'save_batch')
        assert hasattr(file_storage, 'download_content')
        assert hasattr(embedding_service, 'generate_embeddings')
        assert hasattr(csv_processor, 'parse_csv')
        assert hasattr(event_publisher, 'publish_document_processing_started')
    
    def test_use_case_provider(self, container):
        """Test use case provider"""
        use_case = container.process_document_use_case()
        
        assert use_case is not None
        assert hasattr(use_case, 'execute')
    
    def test_singleton_behavior(self, container):
        """Test that providers return the same instance (singleton behavior)"""
        # Test configuration singleton
        config1 = container.config()
        config2 = container.config()
        assert config1 is config2
        
        # Test logger singleton
        logger1 = container.logger()
        logger2 = container.logger()
        assert logger1 is logger2
        
        # Test domain service singleton
        generator1 = container.searchable_text_generator()
        generator2 = container.searchable_text_generator()
        assert generator1 is generator2
        
        # Test use case singleton
        use_case1 = container.process_document_use_case()
        use_case2 = container.process_document_use_case()
        assert use_case1 is use_case2
    
    def test_dependency_injection_wiring(self, container):
        """Test that dependencies are properly injected"""
        use_case = container.process_document_use_case()
        
        # Check that use case has all required dependencies
        assert hasattr(use_case, '_document_repo')
        assert hasattr(use_case, '_embedding_repo')
        assert hasattr(use_case, '_file_storage')
        assert hasattr(use_case, '_csv_processor')
        assert hasattr(use_case, '_embedding_service')
        assert hasattr(use_case, '_validation_service')
        assert hasattr(use_case, '_security_service')
        assert hasattr(use_case, '_event_publisher')
        assert hasattr(use_case, '_logger')
        assert hasattr(use_case, '_metrics')
        assert hasattr(use_case, '_config')
    
    def test_container_override(self, container):
        """Test that container providers can be overridden for testing"""
        # Create a mock configuration
        mock_config = Mock()
        mock_config.get_openai_api_key.return_value = "mock-key"
        
        # Override the config provider
        container.config.override(mock_config)
        
        # Verify the override works
        config = container.config()
        assert config is mock_config
        assert config.get_openai_api_key() == "mock-key"
        
        # Reset the override
        container.config.reset_override()
        
        # Verify it's back to normal
        config = container.config()
        assert config is not mock_config
        assert isinstance(config, EnvironmentConfiguration)
    
    def test_container_reset(self, container):
        """Test that container can be reset"""
        # Get some instances
        config1 = container.config()
        logger1 = container.logger()

        # Override something first so we can reset it
        mock_config = Mock()
        container.config.override(mock_config)

        # Verify override works
        overridden_config = container.config()
        assert overridden_config is mock_config

        # Reset the container
        container.config.reset_override()

        # Get instances again - they should be back to original
        config2 = container.config()
        logger2 = container.logger()

        assert config2 is not mock_config
        assert logger1 is logger2  # Logger should still be the same singleton
    
    @patch.dict(os.environ, {
        'OPENAI_API_KEY': 'test-key-from-env',
        'SUPABASE_URL': 'https://test-env.supabase.co'
    })
    def test_configuration_from_environment(self, container):
        """Test that configuration reads from environment variables"""
        config = container.config()
        
        assert config.get_openai_api_key() == 'test-key-from-env'
        assert config.get_supabase_url() == 'https://test-env.supabase.co'
    
    def test_container_with_missing_environment_variables(self):
        """Test container behavior with missing environment variables"""
        # Temporarily remove required environment variables
        original_env = os.environ.copy()

        try:
            # Remove required variables and test environment marker
            for key in ['OPENAI_API_KEY', 'SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY', 'ENVIRONMENT', 'PYTEST_CURRENT_TEST']:
                if key in os.environ:
                    del os.environ[key]

            # Creating container should raise an error due to missing config
            with pytest.raises(ValueError, match="Missing required environment variables"):
                container = ApplicationContainer()
                config = container.config()  # This should trigger validation
                # Force validation by calling a method that requires the config
                config.get_openai_api_key()

        finally:
            # Restore original environment
            os.environ.clear()
            os.environ.update(original_env)
