import { EventEmitter } from 'events';
import { inject, injectable } from 'tsyringe';
// Removed Baileys imports for mock implementation
import QRCode from 'qrcode';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import {
  IWhatsAppService,
  StartSessionResult,
  SessionConnectionState,
  BaileysConnection
} from '../../domain/services/IWhatsAppService';
import { SessionEntity, SessionStatus } from '../../domain/entities/SessionEntity';
// Removed unused import for mock implementation

/**
 * WhatsApp service implementation using Baileys
 */
@injectable()
export class WhatsAppService extends EventEmitter implements IWhatsAppService {
  private readonly activeSessions = new Map<string, BaileysConnection>();
  private readonly sessionStates = new Map<string, SessionConnectionState>();
  private readonly maxConcurrentSessions: number;
  private readonly qrTimeoutMs: number;
  private readonly reconnectAttempts: number;
  // Removed unused field

  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private configService: IConfigService,
    // Removed unused auth state adapter for mock implementation
  ) {
    super();
    
    // Load configuration
    const config = this.configService.getOptional('MAX_CONCURRENT_SESSIONS', '100');
    this.maxConcurrentSessions = parseInt(config);
    this.qrTimeoutMs = parseInt(this.configService.getOptional('QR_CODE_TIMEOUT_SECONDS', '60')) * 1000;
    this.reconnectAttempts = parseInt(this.configService.getOptional('RECONNECT_ATTEMPTS', '3'));
    // Removed unused field initialization

    this.logger.info('WhatsAppService initialized', {
      maxConcurrentSessions: this.maxConcurrentSessions,
      qrTimeoutMs: this.qrTimeoutMs,
      reconnectAttempts: this.reconnectAttempts
    });
  }

  /**
   * Start a new WhatsApp session
   */
  async startSession(userId: string): Promise<StartSessionResult> {
    this.logger.info('Starting WhatsApp session', { userId });

    // Check concurrent session limit
    if (this.activeSessions.size >= this.maxConcurrentSessions) {
      throw new Error(`Maximum concurrent sessions limit reached: ${this.maxConcurrentSessions}`);
    }

    // Check if session already exists
    if (this.activeSessions.has(userId)) {
      const existing = this.activeSessions.get(userId)!;
      this.logger.warn('Session already exists', { userId, status: existing.status });
      
      return {
        sessionId: existing.userId,
        status: existing.status,
        phoneNumber: existing.phoneNumber,
        qrCode: existing.qrCode
      };
    }

    try {
      // Get session from database
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        throw new Error(`Session not found for user: ${userId}`);
      }

      // Create Baileys connection
      const connection = await this.createBaileysConnection(userId, session);
      this.activeSessions.set(userId, connection);

      // Initialize session state
      const sessionState: SessionConnectionState = {
        userId,
        status: 'initializing',
        reconnectAttempts: 0,
        lastActivity: new Date()
      };
      this.sessionStates.set(userId, sessionState);

      // Start connection (in development mode, simulate QR pending)
      try {
        await connection.connect();
      } catch (error) {
        this.logger.warn('WhatsApp connection failed, simulating QR pending state', {
          userId,
          error: (error as Error).message
        });
        // In development, simulate QR pending state
        connection.status = 'qr_pending';
        connection.qrCode = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      }

      return {
        sessionId: session.sessionId,
        status: connection.status,
        phoneNumber: connection.phoneNumber,
        qrCode: connection.qrCode
      };
    } catch (error) {
      this.logger.error('Failed to start session', {
        userId,
        error: (error as Error).message
      });
      
      // Cleanup on failure
      this.activeSessions.delete(userId);
      this.sessionStates.delete(userId);
      
      throw error;
    }
  }

  /**
   * Reconnect an existing session
   */
  async reconnectSession(userId: string): Promise<StartSessionResult> {
    this.logger.info('Reconnecting WhatsApp session', { userId });

    const existingConnection = this.activeSessions.get(userId);
    if (existingConnection) {
      try {
        await existingConnection.reconnect();
        return {
          sessionId: existingConnection.userId,
          status: existingConnection.status,
          phoneNumber: existingConnection.phoneNumber,
          qrCode: existingConnection.qrCode
        };
      } catch (error) {
        this.logger.warn('Failed to reconnect existing connection, creating new one', {
          userId,
          error: (error as Error).message
        });
        await this.terminateSession(userId);
      }
    }

    // Start new session (which will use existing auth state if available)
    return this.startSession(userId);
  }

  /**
   * Regenerate QR code for pending session
   */
  async regenerateQR(userId: string): Promise<{ qrCode: string }> {
    const connection = this.activeSessions.get(userId);
    if (!connection) {
      throw new Error(`No active session found for user: ${userId}`);
    }

    if (connection.status !== 'qr_pending') {
      throw new Error(`Session is not in QR pending state: ${connection.status}`);
    }

    const qrCode = await connection.generateQR();
    return { qrCode };
  }

  /**
   * Get current session status
   */
  async getSessionStatus(userId: string): Promise<SessionStatus> {
    const connection = this.activeSessions.get(userId);
    if (connection) {
      return connection.status;
    }

    // Check database for persisted session
    const session = await this.sessionRepository.findByUserId(userId);
    return session ? session.status : 'disconnected';
  }

  /**
   * Terminate and cleanup session
   */
  async terminateSession(userId: string): Promise<void> {
    this.logger.info('Terminating WhatsApp session', { userId });

    const connection = this.activeSessions.get(userId);
    if (connection) {
      try {
        await connection.disconnect();
      } catch (error) {
        this.logger.warn('Error during connection disconnect', {
          userId,
          error: (error as Error).message
        });
      }
    }

    // Cleanup
    this.activeSessions.delete(userId);
    this.sessionStates.delete(userId);

    // Update database
    try {
      await this.sessionRepository.updateSessionStatus(userId, 'disconnected', {
        disconnectedAt: new Date()
      });
    } catch (error) {
      this.logger.warn('Failed to update session status in database', {
        userId,
        error: (error as Error).message
      });
    }

    this.logger.info('Session terminated', { userId });
  }

  /**
   * Restore all persisted sessions on startup
   */
  async restoreAllSessions(): Promise<number> {
    this.logger.info('Restoring persisted sessions');

    try {
      const activeSessions = await this.sessionRepository.findAllActive();
      let restoredCount = 0;

      for (const session of activeSessions) {
        try {
          if (session.canReconnect()) {
            await this.startSession(session.userId);
            restoredCount++;
          } else {
            // Mark expired sessions as disconnected
            await this.sessionRepository.updateSessionStatus(session.userId, 'disconnected');
          }
        } catch (error) {
          this.logger.warn('Failed to restore session', {
            userId: session.userId,
            error: (error as Error).message
          });
        }
      }

      this.logger.info('Session restoration completed', {
        totalSessions: activeSessions.length,
        restoredCount
      });

      return restoredCount;
    } catch (error) {
      this.logger.error('Failed to restore sessions', {
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Check if session is active and connected
   */
  async isSessionActive(userId: string): Promise<boolean> {
    const connection = this.activeSessions.get(userId);
    return connection ? connection.status === 'connected' : false;
  }

  /**
   * Get active session count
   */
  async getActiveSessionCount(): Promise<number> {
    return this.activeSessions.size;
  }

  /**
   * Get all active user IDs
   */
  async getActiveUserIds(): Promise<string[]> {
    return Array.from(this.activeSessions.keys());
  }

  /**
   * Send a test message (for connection verification)
   */
  async sendTestMessage(userId: string, phoneNumber: string, message: string): Promise<void> {
    const connection = this.activeSessions.get(userId);
    if (!connection) {
      throw new Error(`No active session found for user: ${userId}`);
    }

    if (connection.status !== 'connected') {
      throw new Error(`Session is not connected: ${connection.status}`);
    }

    await connection.sendMessage(phoneNumber, { text: message });
    this.logger.info('Test message sent', { userId, phoneNumber });
  }

  /**
   * Create a Baileys connection wrapper
   */
  private async createBaileysConnection(userId: string, _session: SessionEntity): Promise<BaileysConnection> {
    // For development, create a mock connection to avoid actual WhatsApp connection
    const connection: BaileysConnection = {
      userId,
      socket: null as any, // Mock socket for development
      status: 'initializing',
      lastActivity: new Date(),
      reconnectAttempts: 0,
      qrCode: undefined,

      async connect() {
        // Mock connection for development
        this.status = 'qr_pending';
        this.qrCode = 'mock-qr-code-data';
      },

      async disconnect() {
        this.status = 'disconnected';
      },

      async reconnect() {
        await this.connect();
      },

      async generateQR() {
        if (!this.qrCode) {
          // Generate a simple mock QR code
          this.qrCode = 'mock-qr-code-' + Date.now();
        }
        return QRCode.toDataURL(this.qrCode);
      },

      async sendMessage(to: string, message: any) {
        // Mock message sending
        console.log(`Mock: Sending message to ${to}:`, message);
      },

      on: () => {},
      off: () => {},
      emit: () => false
    };

    return connection;
  }

  // Removed unused event handlers for mock implementation
}
