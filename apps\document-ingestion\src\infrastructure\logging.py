"""
Logging Infrastructure for Document Ingestion Pipeline

This module provides logging implementation that integrates with the domain interfaces.
"""

import json
import logging
import sys
import traceback
from datetime import datetime, timezone
from typing import Optional, Any, Dict
from ..domain.interfaces import ILogger
from .configuration import EnvironmentConfiguration


class StructuredFormatter(logging.Formatter):
    """Custom formatter that outputs structured JSON logs"""
    
    def __init__(self, config: EnvironmentConfiguration):
        super().__init__()
        self.environment = config.get_environment()
        self.service_name = "document-ingestion"
        self.version = config.get_service_version()
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(
                record.created, tz=timezone.utc
            ).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "service": self.service_name,
            "environment": self.environment,
            "version": self.version,
        }
        
        # Add AWS Lambda context if available
        if hasattr(record, "aws_request_id"):
            log_entry["aws_request_id"] = record.aws_request_id
        
        if hasattr(record, "function_name"):
            log_entry["function_name"] = record.function_name
        
        # Add custom fields from extra parameter
        for attr in ['user_id', 'document_id', 'operation', 'duration_ms', 
                    'error_code', 'metrics', 'status']:
            if hasattr(record, attr):
                log_entry[attr] = getattr(record, attr)
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info),
            }
        
        # Add stack trace for errors
        if record.levelno >= logging.ERROR and not record.exc_info:
            log_entry["stack_trace"] = traceback.format_stack()
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)


class DomainLogger(ILogger):
    """Domain logger implementation using Python logging"""
    
    def __init__(self, config: EnvironmentConfiguration, name: str = "document_ingestion"):
        self.config = config
        self.logger = logging.getLogger(name)
        self._setup_logging()
    
    def _setup_logging(self):
        """Configure logging with structured formatter"""
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, self.config.get_log_level().upper(), logging.INFO)
        self.logger.setLevel(log_level)
        
        # Create console handler with structured formatter
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(StructuredFormatter(self.config))
        
        self.logger.addHandler(handler)
        
        # Prevent duplicate logs
        self.logger.propagate = False
    
    def info(self, message: str, **context) -> None:
        """Log info message"""
        self.logger.info(message, extra=context)
    
    def warning(self, message: str, **context) -> None:
        """Log warning message"""
        self.logger.warning(message, extra=context)
    
    def error(self, message: str, error: Optional[Exception] = None, **context) -> None:
        """Log error message"""
        if error:
            context['error_type'] = type(error).__name__
            self.logger.error(message, extra=context, exc_info=error)
        else:
            self.logger.error(message, extra=context)
    
    def debug(self, message: str, **context) -> None:
        """Log debug message"""
        self.logger.debug(message, extra=context)
    
    def log_document_processing_start(self, user_id: str, document_id: str, s3_key: str):
        """Log start of document processing"""
        self.info(
            "Document processing started",
            user_id=user_id,
            document_id=document_id,
            operation="document_processing",
            status="started",
            s3_key=s3_key
        )
    
    def log_document_processing_complete(self, user_id: str, document_id: str, 
                                       processed_rows: int, duration_ms: float):
        """Log successful completion of document processing"""
        self.info(
            "Document processing completed successfully",
            user_id=user_id,
            document_id=document_id,
            operation="document_processing",
            status="completed",
            duration_ms=duration_ms,
            metrics={"processed_rows": processed_rows}
        )
    
    def log_document_processing_failed(self, user_id: str, document_id: str, 
                                     error: Exception, duration_ms: float):
        """Log failed document processing"""
        self.error(
            "Document processing failed",
            error=error,
            user_id=user_id,
            document_id=document_id,
            operation="document_processing",
            status="failed",
            duration_ms=duration_ms,
            error_code=type(error).__name__
        )
    
    def log_csv_parsing(self, user_id: str, document_id: str, rows: int, 
                       columns: int, file_size_bytes: int):
        """Log CSV parsing results"""
        self.info(
            "CSV parsing completed",
            user_id=user_id,
            document_id=document_id,
            operation="csv_parsing",
            status="completed",
            metrics={
                "rows": rows,
                "columns": columns,
                "file_size_bytes": file_size_bytes,
            }
        )
    
    def log_embedding_generation(self, user_id: str, document_id: str, 
                               batch_count: int, total_embeddings: int, duration_ms: float):
        """Log embedding generation results"""
        self.info(
            "Embedding generation completed",
            user_id=user_id,
            document_id=document_id,
            operation="embedding_generation",
            status="completed",
            duration_ms=duration_ms,
            metrics={
                "batch_count": batch_count,
                "total_embeddings": total_embeddings,
                "embeddings_per_second": total_embeddings / (duration_ms / 1000) if duration_ms > 0 else 0,
            }
        )
    
    def log_supabase_storage(self, user_id: str, document_id: str, 
                           embeddings_stored: int, duration_ms: float):
        """Log Supabase storage results"""
        self.info(
            "Supabase storage completed",
            user_id=user_id,
            document_id=document_id,
            operation="supabase_storage",
            status="completed",
            duration_ms=duration_ms,
            metrics={
                "embeddings_stored": embeddings_stored,
                "storage_rate_per_second": embeddings_stored / (duration_ms / 1000) if duration_ms > 0 else 0,
            }
        )


def setup_lambda_logging(config: EnvironmentConfiguration, context=None) -> DomainLogger:
    """Setup logging for Lambda environment with context"""
    logger = DomainLogger(config)
    
    # Add Lambda context information if available
    if context:
        old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.aws_request_id = getattr(context, "aws_request_id", None)
            record.function_name = getattr(context, "function_name", None)
            return record
        
        logging.setLogRecordFactory(record_factory)
    
    return logger
