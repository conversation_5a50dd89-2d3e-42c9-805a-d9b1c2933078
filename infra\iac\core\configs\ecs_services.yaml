# ECS Services Configuration for api.dev.anchorsprint.com

# Test Service with default route to nginx
test-service:
  container_image: nginx:latest
  container_port: 80
  path_patterns:
    - "/"
  health_check_path: "/health"
  # Custom command to create health check endpoint
  command:
    [
      "/bin/sh",
      "-c",
      'echo ''server { listen 80; location / { root /usr/share/nginx/html; index index.html; } location /health { return 200 "OK"; } }'' > /etc/nginx/conf.d/default.conf && nginx -g ''daemon off;''',
    ]
  # Task CPU and memory settings
  task_cpu: 256 # Minimum CPU for Fargate
  task_memory: 512 # Minimum memory for Fargate
  container_memory: 512 # Explicitly set container memory
  assign_public_ip: true # Enable public IP for tasks in default VPC
  environment_variables:
    SERVICE_NAME: test-service
    LOG_LEVEL: info
  desired_count: 1
  tags:
    Service: Test Service
    Description: Default route to nginx for testing
    schedule: "true" # Enable scheduling for this service

whatsapp-manager:
  container_image: "611032973328.dkr.ecr.ap-southeast-5.amazonaws.com/whatsapp-manager:latest"
  container_port: 3000
  path_patterns:
    - "/api/*"
    - "/health"
  health_check_path: "/health"
  task_cpu: 256
  task_memory: 512
  container_memory: 512
  assign_public_ip: true
  environment_variables:
    HOST: "0.0.0.0"
    PORT: "3000"
    NODE_ENV: "uat"
    AWS_REGION: "ap-southeast-5"
    LOG_LEVEL: "info"
    LOG_FORMAT: "json"
    CORS_ORIGINS: "*"
    DYNAMODB_TABLE_NAME: "WhatsAppSessions-uat"
  # Secrets from AWS Secrets Manager
  secrets:
    "JWT_SECRET": "arn:aws:secretsmanager:ap-southeast-5:611032973328:secret:uat/whatsapp-manager/jwt_token"
  desired_count: 1
  tags:
    Service: "WhatsApp Manager"
    Description: "WhatsApp API Middleware Foundation"
    schedule: "true"
