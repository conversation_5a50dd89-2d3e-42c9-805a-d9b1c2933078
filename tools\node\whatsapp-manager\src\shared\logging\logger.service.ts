import { injectable, inject } from 'tsyringe';
import pino, { Logger } from 'pino';
import { ILoggerService } from './interfaces';
import { IConfigService } from '../config/interfaces';

@injectable()
export class LoggerService implements ILoggerService {
  private readonly logger: Logger;

  constructor(@inject('IConfigService') private configService: IConfigService) {
    const loggingConfig = this.configService.getLogging();

    const pinoOptions: pino.LoggerOptions = {
      level: loggingConfig.level,
      formatters: {
        level: (label: string) => {
          return { level: label };
        },
      },
      timestamp: pino.stdTimeFunctions.isoTime,
      base: {
        pid: process.pid,
        hostname: process.env['HOSTNAME'] || 'unknown',
        service: 'whatsapp-manager',
        environment: this.configService.getEnvironment(),
      },
    };

    if (loggingConfig.format === 'pretty') {
      pinoOptions.transport = {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
        },
      };
    }

    this.logger = pino(pinoOptions);
  }

  debug(message: string, meta?: object): void {
    this.logger.debug(meta, message);
  }

  info(message: string, meta?: object): void {
    this.logger.info(meta, message);
  }

  warn(message: string, meta?: object): void {
    this.logger.warn(meta, message);
  }

  error(message: string | Error, meta?: object): void {
    if (message instanceof Error) {
      this.logger.error({ err: message, ...meta }, message.message);
    } else {
      this.logger.error(meta, message);
    }
  }

  child(bindings: object): ILoggerService {
    const childLogger = this.logger.child(bindings);
    return new ChildLoggerService(childLogger);
  }

  withRequestId(requestId: string): ILoggerService {
    return this.child({ requestId });
  }
}

class ChildLoggerService implements ILoggerService {
  constructor(private readonly logger: Logger) {}

  debug(message: string, meta?: object): void {
    this.logger.debug(meta, message);
  }

  info(message: string, meta?: object): void {
    this.logger.info(meta, message);
  }

  warn(message: string, meta?: object): void {
    this.logger.warn(meta, message);
  }

  error(message: string | Error, meta?: object): void {
    if (message instanceof Error) {
      this.logger.error({ err: message, ...meta }, message.message);
    } else {
      this.logger.error(meta, message);
    }
  }

  child(bindings: object): ILoggerService {
    const childLogger = this.logger.child(bindings);
    return new ChildLoggerService(childLogger);
  }

  withRequestId(requestId: string): ILoggerService {
    return this.child({ requestId });
  }
}
