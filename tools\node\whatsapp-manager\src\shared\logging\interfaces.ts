/**
 * Logging interfaces for structured logging with pino
 */

export interface ILoggerService {
  debug(message: string, meta?: object): void;
  info(message: string, meta?: object): void;
  warn(message: string, meta?: object): void;
  error(message: string | Error, meta?: object): void;
  
  // Structured logging helpers
  child(bindings: object): ILoggerService;
  
  // Request correlation
  withRequestId(requestId: string): ILoggerService;
}

export interface LogContext {
  requestId?: string;
  userId?: string;
  sessionId?: string;
  operation?: string;
  component?: string;
  [key: string]: unknown;
}
